{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tensorboard.backend.event_processing.event_accumulator import EventAccumulator"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["event_smoe = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tensorboard')"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "source": ["# Point to the directory (or file) containing your event files.\n", "event_softmax = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tensorboard')\n", "event_softmax.Reload()  # Load the events\n", "balancing_loss_softmax = event_softmax.Scalars('train/loss')"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["<tensorboard.backend.event_processing.event_accumulator.EventAccumulator at 0x7fe5aa4fbfa0>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Point to the directory (or file) containing your event files.\n", "event_sigmoid = EventAccumulator('/cm/archive/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_deepseek_sigmoidonly/tensorboard')\n", "event_sigmoid.Reload()  # Load the events\n", "event_shared = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/shared/tensorboard')\n", "event_shared.Reload()  # Load the events\n", "event_deepseek = EventAccumulator('/home/<USER>/moeut_training_code/tensorboard/158m/deepseek/tensorboard')\n", "event_deepseek.Reload()  # Load the events\n", "\n", "\n", "steps = list(set([event_smoe.Scalars('train/loss')[i].step for i in range(len(event_smoe.Scalars('train/loss')))]))\n", "steps.sort()\n", "steps_validation = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]\n", "log_info = {\n", "    \"training_loss\": {\n", "        \"smoe\": filter_duplicate_steps(event_smoe.Scalars('train/loss')),\n", "        \"sigmoid\": filter_duplicate_steps(event_sigmoid.Scalars('train/loss')),\n", "        \"shared\": filter_duplicate_steps(event_shared.Scalars('train/loss')),\n", "        \"deepseek\": filter_duplicate_steps(event_deepseek.Scalars('train/loss'))\n", "    },\n", "    \"balancing_loss\": {\n", "        \"smoe\": filter_duplicate_steps(event_smoe.Scalars('train/reg_loss/moe')),\n", "        \"sigmoid\": filter_duplicate_steps(event_sigmoid.Scalars('train/reg_loss/moe')),\n", "        \"shared\": filter_duplicate_steps(event_shared.Scalars('train/reg_loss/moe')),\n", "        \"deepseek\": filter_duplicate_steps(event_deepseek.Scalars('train/reg_loss/moe'))\n", "    },\n", "    \"validation_loss\": {\n", "        \"smoe\": filter_duplicate_steps(event_smoe.Scalars('validation/val/loss')),\n", "        \"sigmoid\": filter_duplicate_steps(event_sigmoid.Scalars('validation/val/loss')),\n", "        \"shared\": filter_duplicate_steps(event_shared.Scalars('validation/val/loss')),\n", "        \"deepseek\": filter_duplicate_steps(event_deepseek.Scalars('validation/val/loss'))\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["balancing_loss_softmax[0].step"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["20"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["balancing_loss_sigmoid[0].step"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["steps = list(set([event.step for event in balancing_loss_softmax]))\n", "\n", "values_softmax = {}\n", "values_sigmoid = {}\n", "for event in balancing_loss_softmax:\n", "    if event.step in values_softmax.keys(): continue\n", "    values_softmax[event.step] = event.value\n", "for event in balancing_loss_sigmoid:\n", "    if event.step in values_sigmoid.keys(): continue\n", "    values_sigmoid[event.step] = event.value\n", "\n", "# Only get intersection of steps\n", "steps = list(set(values_softmax.keys()).intersection(set(values_sigmoid.keys())))\n", "steps.sort()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["(5000, 5000, 5000)"]}, "execution_count": 11, "metadata": {}, "output_type": "display_data"}], "source": ["len(values_softmax.values()), len(values_sigmoid.keys()), len(steps)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "If mode is 'interp', window_length must be less than or equal to the size of x.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[35], line 14\u001b[0m\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# Apply <PERSON>-<PERSON><PERSON> filter for smoothing\u001b[39;00m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# Window length must be odd and less than data length\u001b[39;00m\n\u001b[1;32m     13\u001b[0m window \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m51\u001b[39m  \u001b[38;5;66;03m# Adjust this value to control smoothing amount\u001b[39;00m\n\u001b[0;32m---> 14\u001b[0m y_softmax_smooth \u001b[38;5;241m=\u001b[39m \u001b[43msavgol_filter\u001b[49m\u001b[43m(\u001b[49m\u001b[43my_softmax\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwindow\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m3\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     15\u001b[0m y_sigmoid_smooth \u001b[38;5;241m=\u001b[39m savgol_filter(y_sigmoid, window, \u001b[38;5;241m3\u001b[39m)\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Plot both original (faint) and smoothed lines\u001b[39;00m\n", "File \u001b[0;32m/cm/archive/thongdt4/miniconda3/envs/moeut/lib/python3.10/site-packages/scipy/signal/_savitzky_golay.py:345\u001b[0m, in \u001b[0;36msavgol_filter\u001b[0;34m(x, window_length, polyorder, deriv, delta, axis, mode, cval)\u001b[0m\n\u001b[1;32m    343\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mode \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minterp\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[1;32m    344\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m window_length \u001b[38;5;241m>\u001b[39m x\u001b[38;5;241m.\u001b[39mshape[axis]:\n\u001b[0;32m--> 345\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIf mode is \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124minterp\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, window_length must be less \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    346\u001b[0m                          \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mthan or equal to the size of x.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    348\u001b[0m     \u001b[38;5;66;03m# Do not pad. Instead, for the elements within `window_length // 2`\u001b[39;00m\n\u001b[1;32m    349\u001b[0m     \u001b[38;5;66;03m# of the ends of the sequence, use the polynomial that is fitted to\u001b[39;00m\n\u001b[1;32m    350\u001b[0m     \u001b[38;5;66;03m# the last `window_length` elements.\u001b[39;00m\n\u001b[1;32m    351\u001b[0m     y \u001b[38;5;241m=\u001b[39m convolve1d(x, coeffs, axis\u001b[38;5;241m=\u001b[39maxis, mode\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mconstant\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mValueError\u001b[0m: If mode is 'interp', window_length must be less than or equal to the size of x."]}, {"data": {"text/plain": ["<Figure size 1600x1000 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(16, 10))\n", "\n", "# Convert dictionary values to lists for smoothing\n", "x_values = steps\n", "# only get value in steps\n", "y_softmax = [values_softmax[step] for step in steps]\n", "y_sigmoid = [values_sigmoid[step] for step in steps]\n", "\n", "# Apply <PERSON><PERSON>-<PERSON><PERSON> filter for smoothing\n", "# Window length must be odd and less than data length\n", "window = 51  # Adjust this value to control smoothing amount\n", "y_softmax_smooth = savgol_filter(y_softmax, window, 3)\n", "y_sigmoid_smooth = savgol_filter(y_sigmoid, window, 3)\n", "\n", "# Plot both original (faint) and smoothed lines\n", "plt.plot(x_values, y_softmax, alpha=0.1, color='blue', label='Softmax (raw)')\n", "plt.plot(x_values, y_sigmoid, alpha=0.1, color='orange', label='DeepSeek (raw)')\n", "plt.plot(x_values, y_softmax_smooth, color='blue', label='Softmax (smoothed)', linewidth=2)\n", "plt.plot(x_values, y_sigmoid_smooth, color='orange', label='DeepSeek (smoothed)', linewidth=2)\n", "\n", "plt.title(\"Language Loss\")\n", "plt.grid(True, alpha=0.3)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["[3.0490059852600098,\n", " 2.980741500854492,\n", " 2.854445219039917,\n", " 2.580049514770508,\n", " 2.5422375202178955,\n", " 2.653904438018799,\n", " 2.6716554164886475,\n", " 2.3945062160491943,\n", " 2.446502447128296,\n", " 2.4486124515533447]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["y_softmax"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[10000, 20000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000]\n"]}, {"data": {"text/plain": ["[<matplotlib.lines.Line2D at 0x1553f2494fa0>]"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["steps = list(range(10000, 100001, 10000))\n", "\n", "balancing_loss_softmax_val = event_softmax.Scalars('validation/val/loss')\n", "balancing_loss_sigmoid_val = event_sigmoid.Scalars('validation/val/loss')\n", "print(steps)\n", "\n", "y_softmax = [balancing_loss_softmax_val[int(step/10000) - 1].value for step in steps]\n", "y_sigmoid = [balancing_loss_sigmoid_val[int(step/10000) - 1].value for step in steps]\n", "\n", "plt.plot(steps, y_softmax)\n", "plt.plot(steps, y_sigmoid)\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ScalarEvent(wall_time=1740060969.0192883, step=10000, value=3.0761876106262207),\n", " ScalarEvent(wall_time=1740063579.338595, step=20000, value=2.9264256954193115),\n", " ScalarEvent(wall_time=1740065807.745339, step=30000, value=2.849592447280884),\n", " ScalarEvent(wall_time=1740068037.7421587, step=40000, value=2.7971105575561523),\n", " ScalarEvent(wall_time=1740070264.7053638, step=50000, value=2.755093812942505),\n", " ScalarEvent(wall_time=1740072491.7783883, step=60000, value=2.72265362739563),\n", " ScalarEvent(wall_time=1740074725.3839068, step=70000, value=2.694927453994751),\n", " ScalarEvent(wall_time=1740076967.8816545, step=80000, value=2.672837972640991),\n", " ScalarEvent(wall_time=1740079211.50678, step=90000, value=2.6614596843719482),\n", " ScalarEvent(wall_time=1740081456.601807, step=100000, value=2.6533422470092773)]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["plt.plot"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from scipy.signal import savgol_filter\n", "\n", "plt.figure(figsize=(16, 10))\n", "\n", "# Convert dictionary values to lists for smoothing\n", "x_values = list(values_softmax.keys())\n", "y_softmax = list(values_softmax.values())\n", "y_sigmoid = list(values_sigmoid.values())\n", "\n", "# Apply <PERSON><PERSON>-<PERSON><PERSON> filter for smoothing\n", "# Window length must be odd and less than data length\n", "window = 101  # Adjust this value to control smoothing amount\n", "y_softmax_smooth = savgol_filter(y_softmax, window, 3)\n", "y_sigmoid_smooth = savgol_filter(y_sigmoid, window, 3)\n", "\n", "# Plot both original (faint) and smoothed lines\n", "# plt.plot(x_values, y_softmax, alpha=0.2, color='blue', label='Softmax (raw)')\n", "# plt.plot(x_values, y_sigmoid, alpha=0.2, color='orange', label='Sigmoid (raw)')\n", "plt.plot(x_values, y_softmax_smooth, color='blue', label='Softmax (smoothed)', linewidth=2)\n", "plt.plot(x_values, y_sigmoid_smooth, color='orange', label='Sigmoid (smoothed)', linewidth=2)\n", "\n", "plt.title(\"Load Balancing Loss\")\n", "plt.grid(True, alpha=0.3)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.figure(figsize=(16, 10))\n", "\n", "plt.plot(values_softmax.keys(), values_softmax.values(), label='Softmax')\n", "plt.plot(values_softmax.keys(), values_sigmoid.values(), label='Sigmoid')\n", "\n", "plt.title(\"Load Balancing Loss\")\n", "plt.grid()\n", "plt.legend()  # Add this line to show the legend\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Test checkpoints"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[[0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.0941067 , 0.10167789],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.15945585, 0.        ],\n", "        [0.10725054, 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]],\n", "\n", "       [[0.        , 0.        , 0.18879355, ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.17651786, ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.1414402 , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]],\n", "\n", "       [[0.        , 0.21390268, 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.10881487, 0.06592649, ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]],\n", "\n", "       ...,\n", "\n", "       [[0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.1263696 , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.26124668, 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]],\n", "\n", "       [[0.        , 0.        , 0.        , ..., 0.08339395,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.16795708, ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]],\n", "\n", "       [[0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.19059157, 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        ...,\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ],\n", "        [0.        , 0.        , 0.        , ..., 0.        ,\n", "         0.        , 0.        ]]])"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "\n", "np.load(\"/home/<USER>/moeut_training_code/paper/deepseek/router_saturation_679M/smoe/model-60000.npy\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import torch\n", "checkpoint = torch.load(\"/cm/shared/thongdt4/moeut_training_code/save/test/checkpoint/model-200.pth\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["embedding.weight                                  , 4096000\n", "unique_layers.0.self_attn.data_to_kv.weight       , 335872\n", "unique_layers.0.self_attn.data_to_q.weight        , 167936\n", "unique_layers.0.self_attn.out_proj.weight         , 167936\n", "unique_layers.0.pkm.keys                          , 4194304\n", "unique_layers.0.pkm.keys_shared                   , 131072\n", "unique_layers.0.pkm.values                        , 4194304\n", "unique_layers.0.pkm.values_shared                 , 131072\n", "unique_layers.0.pkm.expert_sel                    , 32768\n", "unique_layers.0.pkm.film_ensemble                 , 512\n", "unique_layers.0.pkm.iter                          , 1\n", "unique_layers.0.pkm.kv_sel_counts_100             , 8192\n", "unique_layers.0.norm1.weight                      , 512\n", "unique_layers.0.norm1.bias                        , 512\n", "unique_layers.0.norm2.weight                      , 512\n", "unique_layers.0.norm2.bias                        , 512\n", "unique_layers.1.self_attn.data_to_kv.weight       , 335872\n", "unique_layers.1.self_attn.data_to_q.weight        , 167936\n", "unique_layers.1.self_attn.out_proj.weight         , 167936\n", "unique_layers.1.pkm.keys                          , 4194304\n", "\n", "Number of parameters:  158057296\n", "\n", "Number of activated parameters:  36946768\n"]}], "source": ["num_params = 0\n", "num_active_params = 0\n", "\n", "for key in list(checkpoint['model'].keys()):\n", "    num_params += checkpoint['model'][key].numel()\n", "\n", "    if \"pkm.keys\" in key or \"pkm.values\" in key:\n", "        num_active_params += int(checkpoint['model'][key].numel() / 8)\n", "    else:\n", "        num_active_params += checkpoint['model'][key].numel()\n", "\n", "for key in list(checkpoint['model'].keys())[:20]:\n", "    print(f\"{key:50}, {checkpoint['model'][key].numel()}\")\n", "\n", "print(\"\\nNumber of parameters: \", num_params)\n", "print(\"\\nNumber of activated parameters: \", num_active_params)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["checkpoint_200 = checkpoint['model'][\"unique_layers.0.pkm.film_ensemble\"]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["checkpoint_100 = checkpoint['model'][\"unique_layers.0.pkm.film_ensemble\"]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([ 0.0776,  0.0894, -0.0498, -0.0271, -0.0560, -0.0539, -0.1156,  0.0390,\n", "         0.0356, -0.1175, -0.0535, -0.0744,  0.0325, -0.1061,  0.1084,  0.0546,\n", "         0.0848,  0.0511,  0.0278,  0.0456,  0.0736, -0.0628,  0.0434,  0.0683,\n", "        -0.0896,  0.0322, -0.0284, -0.0388,  0.0733, -0.0728, -0.0266,  0.1216,\n", "        -0.0294, -0.0294,  0.0522,  0.0524, -0.0662,  0.0844,  0.0249,  0.0352,\n", "         0.0424,  0.0710,  0.0596,  0.0294, -0.0436, -0.0963,  0.0519,  0.0263,\n", "         0.0241, -0.0880, -0.0408, -0.0570, -0.0460,  0.0793, -0.1137, -0.0258,\n", "        -0.0708, -0.0639, -0.0427, -0.1004, -0.0326,  0.0732, -0.0270, -0.0551,\n", "        -0.0369, -0.1467, -0.0490, -0.0801,  0.0571,  0.0245, -0.0334,  0.0414,\n", "         0.0707, -0.0534, -0.0505,  0.0514,  0.1312,  0.0958,  0.0439,  0.0378,\n", "        -0.0645, -0.1135,  0.0879,  0.0963,  0.0739, -0.0973,  0.0708, -0.0424,\n", "        -0.0497,  0.0871,  0.0258,  0.0556,  0.1494,  0.0518,  0.0607,  0.0419,\n", "         0.0627,  0.0702,  0.0645, -0.0710], device='cuda:0')\n", "tensor([ 0.0913,  0.0985, -0.0717, -0.0410, -0.0643, -0.0625, -0.1269,  0.0527,\n", "         0.0606, -0.1301, -0.0591, -0.0884,  0.0545, -0.1187,  0.1245,  0.0646,\n", "         0.1006,  0.0723,  0.0434,  0.0581,  0.0758, -0.0744,  0.0527,  0.0782,\n", "        -0.1103,  0.0413, -0.0421, -0.0458,  0.0844, -0.0790, -0.0321,  0.1301,\n", "        -0.0449, -0.0484,  0.0589,  0.0719, -0.0776,  0.0980,  0.0361,  0.0466,\n", "         0.0560,  0.0836,  0.0746,  0.0450, -0.0580, -0.1087,  0.0697,  0.0420,\n", "         0.0348, -0.1067, -0.0601, -0.0695, -0.0531,  0.0950, -0.1268, -0.0479,\n", "        -0.0849, -0.0735, -0.0565, -0.1111, -0.0419,  0.0932, -0.0384, -0.0673,\n", "        -0.0508, -0.1660, -0.0614, -0.0908,  0.0687,  0.0326, -0.0413,  0.0656,\n", "         0.0870, -0.0580, -0.0630,  0.0634,  0.1514,  0.1216,  0.0556,  0.0477,\n", "        -0.0730, -0.1252,  0.1045,  0.1093,  0.0866, -0.1261,  0.0812, -0.0619,\n", "        -0.0661,  0.1021,  0.0360,  0.0653,  0.1581,  0.0588,  0.0710,  0.0576,\n", "         0.0766,  0.0808,  0.0749, -0.0890], device='cuda:0')\n"]}], "source": ["print(checkpoint_100[:100])\n", "print(checkpoint_200[:100])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Extract result from result json files"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import numpy as np\n", "import os\n", "import glob"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-10000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-20000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-30000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-40000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-50000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-60000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-70000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-80000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-90000.pth.json',\n", " '/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-100000.pth.json']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result_dir = \"/cm/archive/thongdt4/moeut_training_code/save_final/slimpajama_moe_no_attmoe_154M_standard_lb/export\"\n", "# print(os.path.join(result_dir + \"/export\", \"result-model-200000*.json\"))\n", "all_json_files = glob.glob(result_dir + \"/*.json\")\n", "all_json_files = sorted(all_json_files, key=lambda x: int(x.split('-')[-1].split('.')[0]))\n", "# all_json_files = [all_json_files[-1]]\n", "all_json_files"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model_name</th>\n", "      <th>val/perplexity</th>\n", "      <th>lambada/accuracy/total</th>\n", "      <th>blimp/accuracy/group_average</th>\n", "      <th>cbt/accuracy/seq_average</th>\n", "      <th>hellaswag/accuracy/seq_average</th>\n", "      <th>piqa/accuracy/seq_average</th>\n", "      <th>ai2arc/accuracy/ARC-Challenge</th>\n", "      <th>race/accuracy/group_average</th>\n", "      <th>siqa/accuracy/seq_average</th>\n", "      <th>commonsenseqa/accuracy/seq_average</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>result-model-10000.pth.json</td>\n", "      <td>20.615353</td>\n", "      <td>0.162267</td>\n", "      <td>0.719597</td>\n", "      <td>0.780512</td>\n", "      <td>0.269269</td>\n", "      <td>0.549510</td>\n", "      <td>0.210300</td>\n", "      <td>0.288402</td>\n", "      <td>0.370522</td>\n", "      <td>0.235872</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>result-model-20000.pth.json</td>\n", "      <td>17.826648</td>\n", "      <td>0.193517</td>\n", "      <td>0.732149</td>\n", "      <td>0.800620</td>\n", "      <td>0.279426</td>\n", "      <td>0.559304</td>\n", "      <td>0.203433</td>\n", "      <td>0.290722</td>\n", "      <td>0.362334</td>\n", "      <td>0.228501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>result-model-30000.pth.json</td>\n", "      <td>16.532284</td>\n", "      <td>0.200311</td>\n", "      <td>0.750403</td>\n", "      <td>0.819328</td>\n", "      <td>0.281418</td>\n", "      <td>0.578346</td>\n", "      <td>0.216309</td>\n", "      <td>0.293287</td>\n", "      <td>0.365404</td>\n", "      <td>0.232596</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>result-model-40000.pth.json</td>\n", "      <td>15.738304</td>\n", "      <td>0.215062</td>\n", "      <td>0.755925</td>\n", "      <td>0.821829</td>\n", "      <td>0.284206</td>\n", "      <td>0.566376</td>\n", "      <td>0.210300</td>\n", "      <td>0.302047</td>\n", "      <td>0.362845</td>\n", "      <td>0.253071</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>result-model-50000.pth.json</td>\n", "      <td>15.052557</td>\n", "      <td>0.237189</td>\n", "      <td>0.757821</td>\n", "      <td>0.826130</td>\n", "      <td>0.286198</td>\n", "      <td>0.568009</td>\n", "      <td>0.209442</td>\n", "      <td>0.299071</td>\n", "      <td>0.365916</td>\n", "      <td>0.244881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>result-model-60000.pth.json</td>\n", "      <td>14.566057</td>\n", "      <td>0.233890</td>\n", "      <td>0.769269</td>\n", "      <td>0.829932</td>\n", "      <td>0.290181</td>\n", "      <td>0.576714</td>\n", "      <td>0.200000</td>\n", "      <td>0.299089</td>\n", "      <td>0.355681</td>\n", "      <td>0.247338</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>result-model-70000.pth.json</td>\n", "      <td>14.200211</td>\n", "      <td>0.245924</td>\n", "      <td>0.764254</td>\n", "      <td>0.833433</td>\n", "      <td>0.287194</td>\n", "      <td>0.577258</td>\n", "      <td>0.204292</td>\n", "      <td>0.301607</td>\n", "      <td>0.359263</td>\n", "      <td>0.257985</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>result-model-80000.pth.json</td>\n", "      <td>13.883169</td>\n", "      <td>0.249418</td>\n", "      <td>0.773104</td>\n", "      <td>0.838235</td>\n", "      <td>0.292671</td>\n", "      <td>0.570185</td>\n", "      <td>0.207725</td>\n", "      <td>0.306555</td>\n", "      <td>0.358751</td>\n", "      <td>0.249795</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>result-model-90000.pth.json</td>\n", "      <td>13.724526</td>\n", "      <td>0.246312</td>\n", "      <td>0.776030</td>\n", "      <td>0.841136</td>\n", "      <td>0.293368</td>\n", "      <td>0.580522</td>\n", "      <td>0.207725</td>\n", "      <td>0.304429</td>\n", "      <td>0.359263</td>\n", "      <td>0.253890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>result-model-100000.pth.json</td>\n", "      <td>13.625768</td>\n", "      <td>0.252717</td>\n", "      <td>0.777134</td>\n", "      <td>0.841837</td>\n", "      <td>0.294264</td>\n", "      <td>0.579434</td>\n", "      <td>0.212017</td>\n", "      <td>0.301142</td>\n", "      <td>0.356192</td>\n", "      <td>0.246519</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     model_name  val/perplexity  lambada/accuracy/total  \\\n", "0   result-model-10000.pth.json       20.615353                0.162267   \n", "1   result-model-20000.pth.json       17.826648                0.193517   \n", "2   result-model-30000.pth.json       16.532284                0.200311   \n", "3   result-model-40000.pth.json       15.738304                0.215062   \n", "4   result-model-50000.pth.json       15.052557                0.237189   \n", "5   result-model-60000.pth.json       14.566057                0.233890   \n", "6   result-model-70000.pth.json       14.200211                0.245924   \n", "7   result-model-80000.pth.json       13.883169                0.249418   \n", "8   result-model-90000.pth.json       13.724526                0.246312   \n", "9  result-model-100000.pth.json       13.625768                0.252717   \n", "\n", "   blimp/accuracy/group_average  cbt/accuracy/seq_average  \\\n", "0                      0.719597                  0.780512   \n", "1                      0.732149                  0.800620   \n", "2                      0.750403                  0.819328   \n", "3                      0.755925                  0.821829   \n", "4                      0.757821                  0.826130   \n", "5                      0.769269                  0.829932   \n", "6                      0.764254                  0.833433   \n", "7                      0.773104                  0.838235   \n", "8                      0.776030                  0.841136   \n", "9                      0.777134                  0.841837   \n", "\n", "   hellaswag/accuracy/seq_average  piqa/accuracy/seq_average  \\\n", "0                        0.269269                   0.549510   \n", "1                        0.279426                   0.559304   \n", "2                        0.281418                   0.578346   \n", "3                        0.284206                   0.566376   \n", "4                        0.286198                   0.568009   \n", "5                        0.290181                   0.576714   \n", "6                        0.287194                   0.577258   \n", "7                        0.292671                   0.570185   \n", "8                        0.293368                   0.580522   \n", "9                        0.294264                   0.579434   \n", "\n", "   ai2arc/accuracy/ARC-Challenge  race/accuracy/group_average  \\\n", "0                       0.210300                     0.288402   \n", "1                       0.203433                     0.290722   \n", "2                       0.216309                     0.293287   \n", "3                       0.210300                     0.302047   \n", "4                       0.209442                     0.299071   \n", "5                       0.200000                     0.299089   \n", "6                       0.204292                     0.301607   \n", "7                       0.207725                     0.306555   \n", "8                       0.207725                     0.304429   \n", "9                       0.212017                     0.301142   \n", "\n", "   siqa/accuracy/seq_average  commonsenseqa/accuracy/seq_average  \n", "0                   0.370522                            0.235872  \n", "1                   0.362334                            0.228501  \n", "2                   0.365404                            0.232596  \n", "3                   0.362845                            0.253071  \n", "4                   0.365916                            0.244881  \n", "5                   0.355681                            0.247338  \n", "6                   0.359263                            0.257985  \n", "7                   0.358751                            0.249795  \n", "8                   0.359263                            0.253890  \n", "9                   0.356192                            0.246519  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["fields = [\n", "    \"val/perplexity\",\n", "    \"lambada/accuracy/total\",\n", "    \"blimp/accuracy/group_average\",\n", "    \"cbt/accuracy/seq_average\",\n", "    \"hellaswag/accuracy/seq_average\",\n", "    \"piqa/accuracy/seq_average\",\n", "    # \"ai2arc/accuracy/ARC-Easy\",\n", "    \"ai2arc/accuracy/ARC-Challenge\",\n", "    \"race/accuracy/group_average\",\n", "    \"siqa/accuracy/seq_average\",\n", "    \"commonsenseqa/accuracy/seq_average\"\n", "]\n", "\n", "result_list = []\n", "for file_path in all_json_files:\n", "    file_name = os.path.basename(file_path)\n", "    # print(file_path)\n", "    with open(file_path) as f:\n", "        result_data = json.load(f)\n", "\n", "    result_table_item = {}\n", "    result_table_item[\"model_name\"] = file_name\n", "    for field in fields:\n", "        try:\n", "            result_table_item[field] = result_data[field]\n", "        except:\n", "            result_table_item[field] = None\n", "\n", "    result_list.append(result_table_item)\n", "\n", "result_table = pd.DataFrame.from_dict(result_list, orient='columns')\n", "result_table"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Đã lưu vào: ./result_table.xlsx\n"]}], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## test"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"/cm/archive/thongdt4/moeut_training_code/cache/BoolQ/data/dev.jsonl\", \"r\") as f:\n", "    data = [json.loads(line) for line in f]"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["data[0]['answer']"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import os\n", "import glob"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["logit = np.load(\"/cm/archive/thongdt4/moeut_training_code/paper/deepseek/logits/158M/smoe/model-10000.npy\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0.00000000e+00, 3.93916562e-04, 2.10231566e-03, 7.70706320e-05,\n", "       1.25000000e-01, 1.20743993e-03, 4.32451867e-04, 3.89506407e-02,\n", "       3.29691044e-04, 1.55425770e-03, 1.37014460e-04, 1.15336202e-01,\n", "       0.00000000e+00, 1.02760838e-04, 6.55100390e-04, 7.85692222e-03,\n", "       2.51250248e-02, 1.07042542e-04, 1.54997606e-03, 0.00000000e+00,\n", "       7.88132846e-02, 0.00000000e+00, 0.00000000e+00, 1.28451048e-03,\n", "       2.53476738e-03, 9.41974358e-05, 8.56340375e-06, 0.00000000e+00,\n", "       0.00000000e+00, 0.00000000e+00, 0.00000000e+00, 0.00000000e+00,\n", "       1.33589096e-03, 5.48057840e-04, 0.00000000e+00, 3.75847779e-02,\n", "       4.28170188e-06, 0.00000000e+00, 1.28451047e-05, 0.00000000e+00,\n", "       0.00000000e+00, 8.44651312e-02, 2.06035487e-02, 0.00000000e+00,\n", "       4.40758392e-02, 5.42919757e-03, 2.82592315e-04, 1.58851128e-03,\n", "       0.00000000e+00, 3.33287679e-02, 1.62704659e-04, 1.41767142e-02,\n", "       0.00000000e+00, 5.35212690e-04, 1.24794476e-01, 1.84113174e-04,\n", "       2.33352743e-03, 0.00000000e+00, 1.04045356e-03, 5.17572090e-02,\n", "       1.09371789e-01, 3.21127620e-04, 0.00000000e+00, 6.24015220e-02,\n", "       8.56340375e-06, 0.00000000e+00])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["logit[0][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Numerical Experiment"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["numerical_exp_data = np.load(\"/cm/archive/thongdt4/moeut_training_code/numerical_experiments/export/results.npy\")"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["def plot_voronoi_style_error(sample_sizes, errors, std_errors=None, filename='voronoi_style_error.pdf'):\n", "    \"\"\"\n", "    Plot the Voronoi-style error as a function of sample size.\n", "\n", "    Parameters\n", "    ----------\n", "    sample_sizes : list or np.ndarray\n", "        List of sample sizes.\n", "    errors : list or np.ndarray\n", "        List of Voronoi-style errors corresponding to each sample size.\n", "    std_errors : list or np.ndarray, optional\n", "        Standard errors for each point, used for error bars.\n", "    filename : str\n", "        Name of the file to save the plot to.\n", "    title : str\n", "        Title of the plot.\n", "    \"\"\"\n", "    # Ensure the export directory exists\n", "    export_dir = '/cm/archive/thongdt4/moeut_training_code/paper/deepseek/numerical_exp'\n", "    os.makedirs(export_dir, exist_ok=True)\n", "\n", "    # Convert to numpy arrays\n", "    sample_sizes = np.array(sample_sizes)\n", "    errors = np.array(errors)\n", "    if std_errors is not None:\n", "        std_errors = np.array(std_errors)\n", "\n", "    # Fit a power law curve: y = a * x^b\n", "    from scipy.optimize import curve_fit\n", "\n", "    def power_law(x, a, b):\n", "        return a * x**b\n", "\n", "    # Fit the curve\n", "    params, _ = curve_fit(power_law, sample_sizes, errors)\n", "    a, b = params\n", "\n", "    # Generate points for the fitted curve\n", "    x_fit = np.logspace(np.log10(min(sample_sizes)), np.log10(max(sample_sizes)), 100)\n", "    y_fit = power_law(x_fit, a, b)\n", "\n", "    # Create the plot\n", "    plt.figure(figsize=(8, 5))\n", "\n", "    # Plot the fitted curve\n", "    plt.plot(x_fit, y_fit, '--', color='orange', label=f'{a:.2f}n^{b:.5f}')\n", "\n", "    # Plot the data points with error bars if provided\n", "    if std_errors is not None:\n", "        plt.errorbar(sample_sizes, errors, yerr=std_errors, fmt='-o', color='blue', label='D₁(Ĝₙ, G*)')\n", "    else:\n", "        plt.plot(sample_sizes, errors, '-o', color='blue', label='D₁(Ĝₙ, G*)')\n", "\n", "    # Use log scales for both axes to match the example\n", "    plt.xscale('log')\n", "    plt.yscale('log')\n", "    plt.xlabel('log(sample size)')\n", "    plt.ylabel('log(loss)')\n", "    # plt.grid(True, which='both', linestyle='--', alpha=0.2)\n", "    plt.legend()\n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(export_dir, filename))\n", "    plt.show()\n", "    plt.close()\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_voronoi_style_error(numerical_exp_data[0], numerical_exp_data[1], numerical_exp_data[2], filename='voronoi_style_error_sigmoid.pdf')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### Merge the results from different json files into one"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["def merge_json(file1_path, file2_path):\n", "    with open(file1_path) as f:\n", "        result_data1 = json.load(f)\n", "    with open(file2_path) as f:\n", "        result_data2 = json.load(f)\n", "\n", "    # merge data of result_data2 to result_data1 (except for the fields that are already in result_data1), but still update 'cbt' fields\n", "    for key, value in result_data2.items():\n", "        if key in result_data1.keys():\n", "            if \"cbt\" in key:\n", "                result_data1[key] = value\n", "            else:\n", "                continue\n", "        else:\n", "            result_data1[key] = value\n", "\n", "    # save the merged data to a new file\n", "    with open(file1_path, \"w\") as f:\n", "        json.dump(result_data1, f)\n", "\n", "    print(f\"Merge {file1_path} successfully!\")\n", "\n", "# test the function\n", "# file1_path = \"/home/<USER>/moeut_training_code/paper/deepseek/result/result-model-10000.pth-1.json\"\n", "# file2_path = \"/home/<USER>/moeut_training_code/paper/deepseek/result/result-model-10000.pth-2.json\"\n", "# merge_json(file1_path, file2_path)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-40000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-90000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-30000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-50000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-70000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-80000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-20000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-60000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-100000.pth.json successfully!\n", "Merge /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export/result-model-10000.pth.json successfully!\n"]}], "source": ["foler_result_1 = \"/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/export\"\n", "foler_result_2 = \"/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_standard_lb/tmp\"\n", "\n", "# loop through all files in folder_result_2 and merge them to the corresponding files in folder_result_1\n", "for file_name in os.listdir(foler_result_2):\n", "    file1_path = os.path.join(foler_result_1, file_name)\n", "    file2_path = os.path.join(foler_result_2, file_name)\n", "    merge_json(file1_path, file2_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## TMP"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import json"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["with open(\"/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_sigmoid_standard_lb/tmp/result-model-90000.pth.json\") as f:\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.847639055622249"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["data[\"cbt/accuracy/seq_average\"]"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Answer': {'Aliases': ['David <PERSON>'],\n", "  'MatchedWikiEntityName': 'David <PERSON>',\n", "  'NormalizedAliases': ['david se<PERSON>'],\n", "  'NormalizedMatchedWikiEntityName': 'david se<PERSON>',\n", "  'NormalizedValue': 'david seville',\n", "  'Type': 'WikipediaEntity',\n", "  'Value': '<PERSON>'},\n", " 'EntityPages': [],\n", " 'Question': 'Who was the man behind The Chipmunks?',\n", " 'QuestionId': 'tc_2',\n", " 'QuestionSource': 'http://www.triviacountry.com/',\n", " 'SearchResults': [{'Description': \"<PERSON>'s The Man Behind the Mask Music Video. Chipmunk styled\",\n", "   'DisplayUrl': 'www.youtube.com/watch?v=EFme-4S6l3Y',\n", "   'Rank': 0,\n", "   'Title': 'The Man Behind the Mask (Chipmunk Version) - YouTube',\n", "   'Url': 'http://www.youtube.com/watch?v=EFme-4S6l3Y'},\n", "  {'Description': 'Listen to all the actors who have voiced the following Alvin & The Chipmunks characters ... One Punch Man. 3. <PERSON> ... Inyxception Enterprises, Inc. DBA Behind The ...',\n", "   'DisplayUrl': 'www.behindthevoiceactors.com/tv-shows/<PERSON>-and-The-Chipmunks',\n", "   'Rank': 1,\n", "   'Title': 'Alvin & The Chipmunks - Behind The Voice Actors - Images ...',\n", "   'Url': 'http://www.behindthevoiceactors.com/tv-shows/<PERSON>-and-The-Chipmunks/'},\n", "  {'Description': 'The Easter Chipmunk. US Premiere: Apr 14, 1995. ... One Punch Man. 3. <PERSON> ... All original content © 2009-2016 Inyxception Enterprises, Inc. DBA Behind The Voice ...',\n", "   'DisplayUrl': 'www.behindthevoiceactors.com/tv-shows/The-Easter-Chipmunk',\n", "   'Rank': 2,\n", "   'Title': 'The Easter Chipmunk - Cast Images | Behind The Voice Actors',\n", "   'Url': 'http://www.behindthevoiceactors.com/tv-shows/The-Easter-Chipmunk/'},\n", "  {'Description': '<PERSON> is one of the Chipmunks and the main protagonist ... his role as band front man, ... charging <PERSON> orders them to leave pop stardom behind and ...',\n", "   'DisplayUrl': 'aatc.wikia.com/wiki/<PERSON>_<PERSON>',\n", "   'Rank': 3,\n", "   'Title': '<PERSON> - <PERSON> and the Chipmunks Wiki - Wikia',\n", "   'Url': 'http://aatc.wikia.com/wiki/<PERSON>_<PERSON>'},\n", "  {'Description': 'A struggling songwriter named <PERSON> finds success when he comes across a trio of singing chipmunks: ... Title: <PERSON> and the Chipmunks (2007) ...',\n", "   'DisplayUrl': 'www.imdb.com/title/tt0952640',\n", "   'Filename': '61/61_97.txt',\n", "   'Rank': 4,\n", "   'Title': '<PERSON> and the Chipmunks (2007) - IMDb',\n", "   'Url': 'http://www.imdb.com/title/tt0952640/'},\n", "  {'Description': '<PERSON> And The Chipmunks: Behind The Movie (Exclusive) Home. Mail; Flickr; Tumblr; News; Sports; Finance; Celebrity; Answers; Groups; Mobile; More; Install the new ...',\n", "   'DisplayUrl': 'https://www.yahoo.com/movies/video/alvin-chipmunks-behind-movie...',\n", "   'Rank': 5,\n", "   'Title': '<PERSON> And The Chipmunks: Behind The Movie (Exclusive) Video',\n", "   'Url': 'https://www.yahoo.com/movies/video/alvin-chipmunks-behind-movie-exclusive-001000203.html'},\n", "  {'Description': \"The man who brought the Chipmunks to life, ... Five more Chipmunks singles charted in the early '60s, ... See Behind-the-Scenes Rehearsal Photos of <PERSON>'s 'The Passion'\",\n", "   'DisplayUrl': 'www.billboard.com/artist/393411/chipmunks/biography',\n", "   'Filename': '10/10_99.txt',\n", "   'Rank': 6,\n", "   'Title': '<PERSON> Chipmunks - Biography | Billboard',\n", "   'Url': 'http://www.billboard.com/artist/393411/chipmunks/biography'},\n", "  {'Description': '<PERSON> and the Chipmunks (2007) cast and crew credits, including actors, actresses, directors, writers and more. IMDb Movies, TV & Showtimes. MOVIES. In ...',\n", "   'DisplayUrl': 'www.imdb.com/title/tt0952640/fullcredits',\n", "   'Rank': 7,\n", "   'Title': '<PERSON> and the Chipmunks (2007) - Full Cast & Crew - IMDb',\n", "   'Url': 'http://www.imdb.com/title/tt0952640/fullcredits'},\n", "  {'Description': 'This is the Making A Scene Featurette for <PERSON> And The Chipmunks: ... Behind the Scenes of The Amazing Spider-Man ... Chipmunks: Chipwrecked behind ...',\n", "   'DisplayUrl': 'www.youtube.com/watch?v=AllLaG75UbQ',\n", "   'Rank': 8,\n", "   'Title': '<PERSON> And The Chipmunks: The Squeakquel \"Making a Scene ...',\n", "   'Url': 'http://www.youtube.com/watch?v=AllLaG75UbQ'},\n", "  {'Description': '... couldn’t the folks behind ... couldn’t the folks behind the Alvin films have had the good grace to turn <PERSON> and the Chipmunks: ... Michigan man ...',\n", "   'DisplayUrl': 'www.ew.com/article/2012/07/28/alvin-and-chipmunks-squeakquel',\n", "   'Filename': '16/16_102.txt',\n", "   'Rank': 9,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel | EW.com',\n", "   'Url': 'http://www.ew.com/article/2012/07/28/alvin-and-chipmunks-squeakquel'},\n", "  {'Description': '<PERSON> and the Chipmunks: The Road Chip ... where the most rational thing in the world is to pull back the curtain and reveal the incredibly slow-singing man behind ...',\n", "   'DisplayUrl': 'www.slantmagazine.com/film/review/alvin-and-the-chipmunks-the-road...',\n", "   'Rank': 10,\n", "   'Title': '<PERSON> and the Chipmunks: The Road Chip | Film Review ...',\n", "   'Url': 'http://www.slantmagazine.com/film/review/alvin-and-the-chipmunks-the-road-chip'},\n", "  {'Description': '... called <PERSON> And The Chipmunks, while a “man” named <PERSON> ... who is the voice behind the original Chipmunks as well as ... The Chipmunks Christmas Song ...',\n", "   'DisplayUrl': 'www.christmassongs.net/chipmunks-christmas-song',\n", "   'Rank': 11,\n", "   'Title': '<PERSON><PERSON><PERSON> Christmas Song - History, Lyrics, and More ...',\n", "   'Url': 'http://www.christmassongs.net/chipmunks-christmas-song'},\n", "  {'Description': \"The Voices Behind The Singing Chipmunks. ... Funny Man <PERSON> in 'Alvin and the Chipmunks' posted on Monday, December 24, 2007; Follow us on. Follow @ctctweets;\",\n", "   'DisplayUrl': 'www.clickthecity.com/.../2602/the-voices-behind-the-singing-chipmunks',\n", "   'Rank': 12,\n", "   'Title': 'The Voices Behind The Singing Chipmunks - ClickTheCity.com',\n", "   'Url': 'http://www.clickthecity.com/movies/a/2602/the-voices-behind-the-singing-chipmunks'},\n", "  {'Description': '<PERSON> and the Chipmunks: Behind the Music. ... I had followed <PERSON> and the Chipmunks since they first scurried into our ... \"That man knew music better than anybody ...',\n", "   'DisplayUrl': 'www.huffingtonpost.com/doug-lieblich/alvin-and-the-chipmunks-b_b...',\n", "   'Rank': 13,\n", "   'Title': '<PERSON> and the Chipmunks: Behind the Music',\n", "   'Url': 'http://www.huffingtonpost.com/doug-lieblich/alvin-and-the-chipmunks-b_b_792790.html'},\n", "  {'Description': '<PERSON> and the Chipmunks: The Squeakquel is a 2009 CGI/live-action film, ... they dance and sing with the company JETT RECORDS sign behind them.',\n", "   'DisplayUrl': 'alvin.wikia.com/wiki/<PERSON>_and_the_Chipmunks:_The_Squeakquel',\n", "   'Rank': 14,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel - <PERSON> and the ...',\n", "   'Url': 'http://alvin.wikia.com/wiki/<PERSON>_and_the_Chipmunks:_The_Squeakquel'},\n", "  {'Description': \"... couldn't the folks behind the Alvin films have had the good grace to turn <PERSON> and the Chipmunks: ... <PERSON> Explains the Story Behind ... 'Spider-Man ...\",\n", "   'DisplayUrl': 'www.movies.com/movie-reviews/alvin-chipmunks-squeakquel-review/m60012',\n", "   'Rank': 15,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel - Movie Trailers',\n", "   'Url': 'http://www.movies.com/movie-reviews/alvin-chipmunks-squeakquel-review/m60012'},\n", "  {'Description': '<PERSON> is one of The Chipmunks and the ... <PERSON> greatly relishes his role as band front man and his ... Start a Discussion Discussions about <PERSON>',\n", "   'DisplayUrl': 'alvin.wikia.com/wiki/<PERSON>',\n", "   'Filename': '137/137_109.txt',\n", "   'Rank': 16,\n", "   'Title': '<PERSON> - <PERSON> and the Chipmunks Wiki - Wikia',\n", "   'Url': 'http://alvin.wikia.com/wiki/<PERSON>_<PERSON>'},\n", "  {'Description': 'Cartoons <PERSON> and the chipmunks. ... <PERSON> ran up and climbed the railing with <PERSON> following right behind her, ... \"Of course sir\" the man replied in a soft ...',\n", "   'DisplayUrl': 'https://www.fanfiction.net/s/8010491/5/<PERSON>-and-the-Chipmunks...',\n", "   'Rank': 17,\n", "   'Title': '<PERSON> and the Chipmunks: Titanic 2 Chapter 5, an alvin and ...',\n", "   'Url': 'https://www.fanfiction.net/s/8010491/5/<PERSON>-and-the-Chipmunks-Titanic-2'},\n", "  {'Description': \"<PERSON> & the Chipmunks: ... He is always the voice of reason and logic behind <PERSON>'s crazy ... <PERSON><PERSON> decided to leave them with a nice man who was always kind to ...\",\n", "   'DisplayUrl': 'www.tv.com/shows/alvin-and-the-chipmunks',\n", "   'Rank': 18,\n", "   'Title': 'Alvin & the Chipmunks - TV.com',\n", "   'Url': 'http://www.tv.com/shows/alvin-and-the-chipmunks/'},\n", "  {'Description': '“We don’t want the dead chipmunk to eat our food. ... and there we prepare for further surprise as a pretty woman behind the counter asks what this man wants.',\n", "   'DisplayUrl': 'www.believermag.com/issues/201102/?read=article_bachelder',\n", "   'Rank': 19,\n", "   'Title': 'The Believer - The Dead Chipmunk',\n", "   'Url': 'http://www.believermag.com/issues/201102/?read=article_bachelder'},\n", "  {'Description': 'Chipmunks Movies, Alvin And The ... including production stills, premiere photos and other event photos, publicity photos, behind-the-scenes, and more. ... man oh man ...',\n", "   'DisplayUrl': 'https://www.pinterest.com/nicoleturofsky/chipmunks',\n", "   'Rank': 20,\n", "   'Title': 'Chipmunks on Pinterest | The Chipettes, Movie Wallpapers ...',\n", "   'Url': 'https://www.pinterest.com/nicoleturofsky/chipmunks/'},\n", "  {'Description': 'A Christmas Story is a 1983 American ... director of the critically reviled Porky\\'s was the man behind the ... (who he jokingly calls the \"Chicago Chipmunks\") ...',\n", "   'DisplayUrl': 'christmas-specials.wikia.com/wiki/A_Christmas_Story',\n", "   'Filename': '87/87_114.txt',\n", "   'Rank': 21,\n", "   'Title': 'A Christmas Story - Christmas Specials Wiki - Wikia',\n", "   'Url': 'http://christmas-specials.wikia.com/wiki/A_Christmas_Story'},\n", "  {'Description': '<PERSON> and <PERSON> are a pair of cunning and mischievous chipmunks. ... <PERSON> and <PERSON> are a pair of cunning ... where they are the culprits behind the mysterious ...',\n", "   'DisplayUrl': 'disney.wikia.com/wiki/<PERSON>_and_<PERSON>',\n", "   'Filename': '185/185_115.txt',\n", "   'Rank': 22,\n", "   'Title': '<PERSON> and Dale - Disney Wiki - Wikia',\n", "   'Url': 'http://disney.wikia.com/wiki/<PERSON>_and_<PERSON>'},\n", "  {'Description': \"An Alvin & the Chipmunks Community ... He is always the voice of reason and logic behind <PERSON>'s ... <PERSON><PERSON> decided to leave them with a nice man who was ...\",\n", "   'DisplayUrl': 'www.tv.com/shows/alvin-and-the-chipmunks/community',\n", "   'Rank': 23,\n", "   'Title': 'Alvin & the Chipmunks Community - TV.com',\n", "   'Url': 'http://www.tv.com/shows/alvin-and-the-chipmunks/community/'},\n", "  {'Description': '<PERSON> And The Chipmunks 2: ... This man was at \"The Chipmunks\" concert ... <PERSON> hid behind a mailbox and watched from a distance as <PERSON> entered the building ...',\n", "   'DisplayUrl': 'https://www.fanfiction.net/s/4213505/29/<PERSON>-And-The-Chipmunks-2...',\n", "   'Rank': 24,\n", "   'Title': '<PERSON> And The Chipmunks 2: The Chipettes Chapter 29 ...',\n", "   'Url': 'https://www.fanfiction.net/s/4213505/29/<PERSON>-And-The-Chipmunks-2-The-Chipettes'},\n", "  {'Description': 'News & Interviews for <PERSON> and the Chipmunks ... But those behind the murderous plot have no idea what ... with help from a witness (<PERSON>, Iron Man 2), ...',\n", "   'DisplayUrl': 'https://www.rottentomatoes.com/m/alvin_and_the_chipmunks_the...',\n", "   'Rank': 25,\n", "   'Title': '<PERSON> and the Chipmunks - The Mystery of the Easter ...',\n", "   'Url': 'https://www.rottentomatoes.com/m/alvin_and_the_chipmunks_the_mystery_of_the_easter_chipmunk/'},\n", "  {'Description': 'baby’s behind; shirtless men, ... a young man hits a chipmunk like a golf ball; ... “<PERSON> and the Chipmunks: ...',\n", "   'DisplayUrl': 'www.imom.com/movie/alvin-and-the-chipmunks-the-road-chip',\n", "   'Rank': 26,\n", "   'Title': '<PERSON> and the Chipmunks: The Road Chip - iMom',\n", "   'Url': 'http://www.imom.com/movie/alvin-and-the-chipmunks-the-road-chip/'},\n", "  {'Description': 'Characters argue. A man is hit in the crotch. ... <PERSON> and the Chipmunks: ... - <PERSON><PERSON><PERSON>: Behind the Squeaking',\n", "   'DisplayUrl': 'parentpreviews.com/.../alvin-and-the-chipmunks-the-squeakquel',\n", "   'Rank': 27,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel Rating & Info',\n", "   'Url': 'http://parentpreviews.com/movie-reviews/film-info/alvin-and-the-chipmunks-the-squeakquel'},\n", "  {'Description': 'The page <PERSON> & the Chipmunks (Disaster Movie) contains mature content that may include coarse language, ... Man-Eaters. Predator. Cannibals. Hungry Villains ...',\n", "   'DisplayUrl': 'villains.wikia.com/wiki/<PERSON>_&_the_Chipmunks_(Disaster_Movie)',\n", "   'Rank': 28,\n", "   'Title': '<PERSON> & the Chipmunks (Disaster Movie) - Villains Wiki - Wikia',\n", "   'Url': 'http://villains.wikia.com/wiki/<PERSON>_%26_the_Chipmunks_(Disaster_Movie)'},\n", "  {'Description': '... who\\'s varied credits include Run DMC and \"<PERSON> and the Chipmunks.\" ... Three Black Musicians Keeping the ... But in fact the man behind the handle ...',\n", "   'DisplayUrl': 'www.blackenterprise.com/career/men-behind-the-grammys-legacy-chris...',\n", "   'Rank': 29,\n", "   'Title': 'Black Musicians Behind the Grammys: Chris Classic',\n", "   'Url': 'http://www.blackenterprise.com/career/men-behind-the-grammys-legacy-chris-classic/'},\n", "  {'Description': \"<PERSON> & the Chipmunks ... He is always the voice of reason and logic behind <PERSON>'s ... <PERSON><PERSON> decided to leave them with a nice man who was always ...\",\n", "   'DisplayUrl': 'www.metacritic.com/tv/alvin-the-chipmunks/season-3',\n", "   'Rank': 30,\n", "   'Title': '<PERSON> & the Chipmunks - Season 3 Reviews - Metacritic',\n", "   'Url': 'http://www.metacritic.com/tv/alvin-the-chipmunks/season-3'},\n", "  {'Description': 'I loved the jewels hidden in the chipmunk dolls, the creepy <PERSON> man and his ... photos, publicity photos, behind-the ... <PERSON> and the Chipmunks Alvin ...',\n", "   'DisplayUrl': 'https://www.pinterest.com/Kristimari123/alvin-and-the-chipmunks',\n", "   'Rank': 31,\n", "   'Title': '<PERSON> and the Chipmunks on Pinterest | Chipmunks, The ...',\n", "   'Url': 'https://www.pinterest.com/Kristimari123/alvin-and-the-chipmunks/'},\n", "  {'Description': '<PERSON><PERSON> and the Chipmunks ... the entire group goes overboard and is left behind by the ... bizarre comments, and antics with straight man <PERSON> provide ...',\n", "   'DisplayUrl': 'www.comingsoon.net/.../85163-alvin-and-the-chipmunks-chipwrecked-2',\n", "   'Rank': 32,\n", "   'Title': '<PERSON> and the Chipmunks - Chipwrecked - ComingSoon.net',\n", "   'Url': 'http://www.comingsoon.net/movies/reviews/85163-alvin-and-the-chipmunks-chipwrecked-2'},\n", "  {'Description': 'That <PERSON> ~<PERSON>~ ... <PERSON>,<PERSON><PERSON>,and <PERSON> followed close behind. ... Suddenly,I saw a man in a silky red robe brushing his teeth ...',\n", "   'DisplayUrl': 'https://www.quotev.com/story/2704811/That-One-<PERSON><PERSON>-<PERSON>...',\n", "   'Rank': 33,\n", "   'Title': 'That One Chipette ~<PERSON>~ - Quotev',\n", "   'Url': 'https://www.quotev.com/story/2704811/That-<PERSON>-<PERSON><PERSON>-<PERSON>-<PERSON>-Love-Story/1'},\n", "  {'Description': '<PERSON> Alvin and the Chipmunks: ... The police man is SO dramatic. ... Leaving the Chipettes behind was the first mistake.',\n", "   'DisplayUrl': 'https://www.amazon.com/<PERSON>-Road-<PERSON>-<PERSON>/dp/B019EKV5HS',\n", "   'Rank': 34,\n", "   'Title': '<PERSON> and the Chipmunks: The Road Chip - amazon.com',\n", "   'Url': 'https://www.amazon.com/<PERSON>-Chipmunks-<PERSON>-<PERSON>-<PERSON>/dp/B019EKV5HS'},\n", "  {'Description': 'Read about the <PERSON> and the Chipmunks DVD and browse other DVD movies. ... \" performed by <PERSON> and the Chipmunks Behind the Nuts munkumentary ... \\'Spider-Man ...',\n", "   'DisplayUrl': 'www.movies.com/dvd-movies/alvin-chipmunks-dvd/m60011',\n", "   'Rank': 35,\n", "   'Title': '<PERSON> and the Chipmunks DVD | Movies.com',\n", "   'Url': 'http://www.movies.com/dvd-movies/alvin-chipmunks-dvd/m60011'},\n", "  {'Description': \"<PERSON> and the chipmunks ... CHAPTER 11: Meet The Chippetes Part 2 ... there was one person he wasn't allowed to be permitted at the Chipmunks concert, a man who ...\",\n", "   'DisplayUrl': 'https://www.wattpad.com/9012885-alvin-and-the-chipmunks-chippets...',\n", "   'Rank': 36,\n", "   'Title': '<PERSON> and the chipmunks (<PERSON><PERSON><PERSON> story) *Completed ...',\n", "   'Url': 'https://www.wattpad.com/9012885-alvin-and-the-chipmunks-chippets-story-completed'},\n", "  {'Description': 'One tree being cut down was the tree that <PERSON> and the chipmunks live in. ... There was another man behind him. He heard the chipmunks talking when he ...',\n", "   'DisplayUrl': 'https://lettersofflame.wordpress.com/2016/03/22/the-story-of-alvin...',\n", "   'Rank': 37,\n", "   'Title': 'The Story of Alvin and the Chipmunks – Letters of Flame',\n", "   'Url': 'https://lettersofflame.wordpress.com/2016/03/22/the-story-of-alvin-and-the-chipmunks/'},\n", "  {'Description': '\"ALVIN AND THE CHIPMUNKS: THE ... We see the nurse administer the shot into <PERSON>\\'s <PERSON><PERSON> behind. ... forcing the irresponsible young man to be the chipmunks\\' new ...',\n", "   'DisplayUrl': 'https://www.screenit.com/movies/2009/alvin_and_the_chipmunks_the...',\n", "   'Rank': 38,\n", "   'Title': '\"ALVIN AND THE CHIPMUNKS: THE SQUEAKQUEL\" - Screen It',\n", "   'Url': 'https://www.screenit.com/movies/2009/alvin_and_the_chipmunks_the_squeakquel.html'},\n", "  {'Description': '<PERSON> Alvin and the Chipmunks: ... The police man is SO dramatic. ... Leaving the Chipettes behind was the first mistake.',\n", "   'DisplayUrl': 'https://www.amazon.com/<PERSON>-Road-<PERSON>-<PERSON>/dp/B019WMTUZC',\n", "   'Rank': 39,\n", "   'Title': 'Amazon.com: <PERSON> and the Chipmunks: The Road Chip: <PERSON> ...',\n", "   'Url': 'https://www.amazon.com/<PERSON>-Chipmunks-<PERSON>-<PERSON>-<PERSON>/dp/B019WMTUZC'},\n", "  {'Description': \"al<PERSON> and the chipmunks: ... behind them, he also saw ... dave's best friend since elementary school turned rival and the man that the chipmunks hated in the third ...\",\n", "   'DisplayUrl': 'ideas.wikia.com/wiki/ALVIN_AND_THE_CHIPMUNKS:_EMERGENCY_RECALL',\n", "   'Rank': 40,\n", "   'Title': 'ALVIN AND THE CHIPMUNKS: EMERGENCY RECALL - Idea Wiki - Wikia',\n", "   'Url': 'http://ideas.wikia.com/wiki/ALVIN_AND_THE_CHIPMUNKS:_EMERGENCY_RECALL'},\n", "  {'Description': '<PERSON> And The Chipmunks: Chipwrecked Transcript. ... interested in becoming a man to believe ... In a cave behind the waterfall.',\n", "   'DisplayUrl': 'school.wikia.com/wiki/<PERSON>_<PERSON>_The_Chipmunks:_Chipwrecked_Transcript',\n", "   'Rank': 41,\n", "   'Title': '<PERSON> And The Chipmunks: Chipwrecked Transcript - School ...',\n", "   'Url': 'http://school.wikia.com/wiki/<PERSON>_<PERSON>_The_Chipmunks:_Chipwrecked_Transcript'},\n", "  {'Description': '... <PERSON> and the Chipmunks: ... The Chipmunks: Behind the Squeaking; ... <PERSON> is essentially a man who never matured past 15.',\n", "   'DisplayUrl': 'www.gamevortex.com/.../alvin-and-the-chipmunks-the-squeakquel-dvd.html',\n", "   'Rank': 42,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel - GameVortex',\n", "   'Url': 'http://www.gamevortex.com/gamevortex/movie_rev.php/1725/alvin-and-the-chipmunks-the-squeakquel-dvd.html'},\n", "  {'Description': 'In ALVIN AND THE CHIPMUNKS: THE ROAD CHIP, ... a man gets hit in his private area; ... who proceeds to torture the Chipmunks behind their <PERSON> and <PERSON><PERSON>’s backs.',\n", "   'DisplayUrl': 'https://www.movieguide.org/reviews/alvin-and-the-chipmunks-the...',\n", "   'Rank': 43,\n", "   'Title': 'ALVIN AND THE CHIPMUNKS THE ROAD CHIP - Movieguide',\n", "   'Url': 'https://www.movieguide.org/reviews/alvin-and-the-chipmunks-the-road-chip.html'},\n", "  {'Description': 'Definately doesn’t look like the home of a straight single man! ... the houses in Alvin and the Chipmunks movie and ... love knowing all the stuff behind the ...',\n", "   'DisplayUrl': 'hookedonhouses.net/2009/...court-featured-in-alvin-and-the-chipmunks',\n", "   'Rank': 44,\n", "   'Title': 'The Bungalow Court Featured in \"Alvin and the Chipmunks\"',\n", "   'Url': 'http://hookedonhouses.net/2009/12/06/bungalow-court-featured-in-alvin-and-the-chipmunks/'},\n", "  {'Description': \"The teaser trailer for '<PERSON> & the Chipmunks: ... <PERSON><PERSON><PERSON><PERSON><PERSON> was scripted by the writers behind <PERSON> & the Chipmunks: ... the man responsible for films like <PERSON><PERSON> ...\",\n", "   'DisplayUrl': 'screenrant.com/alvin-and-chipmunks-3-trailer-sandy-125917',\n", "   'Rank': 45,\n", "   'Title': '<PERSON><PERSON> & the Chipmunks 3′ Trailer Can Read Your Mind',\n", "   'Url': 'http://screenrant.com/alvin-and-chipmunks-3-trailer-sandy-125917/'},\n", "  {'Description': \"<PERSON> and the Chipmunks: The Squeakquel. ... had a decent running plotline about making fun of the miserable behind the scenes machinery ... man. It's Chipmunk Town ...\",\n", "   'DisplayUrl': 'thatguywiththeglasses.wikia.com/wiki/<PERSON>_and_the_Chipmunks:_The...',\n", "   'Filename': '126/126_139.txt',\n", "   'Rank': 46,\n", "   'Title': '<PERSON> and the Chipmunks: The Squeakquel - Channel Awesome',\n", "   'Url': 'http://thatguywiththeglasses.wikia.com/wiki/<PERSON>_and_the_Chipmunks:_The_Squeakquel'},\n", "  {'Description': 'Great shows: The Chipmunks ... they are only there to be an object that the man wants or to coerce their boyfriend to being funny. ... falling behind schedule, ...',\n", "   'DisplayUrl': 'www.retrojunk.com/article/show/3365/great-shows-the-chipmunks',\n", "   'Rank': 47,\n", "   'Title': 'Great shows: The Chipmunks | Retro Junk Article',\n", "   'Url': 'http://www.retrojunk.com/article/show/3365/great-shows-the-chipmunks'},\n", "  {'Description': 'He was the last of the Chipmunks. ... So <PERSON> reached behind his head and tried to yank out the ... “It was a crabby old man who was seeing the end of his era ...',\n", "   'DisplayUrl': 'grantland.com/features/larry-merchant-leonard-shecter-chipmunks...',\n", "   'Rank': 48,\n", "   'Title': '<PERSON>, <PERSON>, and the Chipmunks ...',\n", "   'Url': 'http://grantland.com/features/larry-merchant-leonard-shecter-chipmunks-sportswriting-clan/'},\n", "  {'Description': 'She has heard that <PERSON> and the chipmunks and the chipetts got famous there , ... \"oh man .. I must have lost my ... I the look behind me and see a big red piano . ...',\n", "   'DisplayUrl': 'https://www.quotev.com/story/7177174/<PERSON>-and-the-chipmunks-x...',\n", "   'Rank': 49,\n", "   'Title': '<PERSON> and the chipmunks x reader - Quotev',\n", "   'Url': 'https://www.quotev.com/story/7177174/<PERSON>-and-the-chipmunks-x-reader/1'}]}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["data['Data'][0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze DeepSeek"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["checkpoint = torch.load(\"/cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_154M_deepseek/checkpoint/model-100000.pth\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["embedding.weight                                  , 4096000\n", "unique_layers.0.self_attn.data_to_kv.weight       , 335872\n", "unique_layers.0.self_attn.data_to_q.weight        , 167936\n", "unique_layers.0.self_attn.out_proj.weight         , 167936\n", "unique_layers.0.pkm.keys                          , 4194304\n", "unique_layers.0.pkm.keys_shared                   , 131072\n", "unique_layers.0.pkm.values                        , 4194304\n", "unique_layers.0.pkm.values_shared                 , 131072\n", "unique_layers.0.pkm.expert_sel                    , 32768\n", "unique_layers.0.pkm.e_score_correction_bias       , 64\n", "unique_layers.0.pkm.iter                          , 1\n", "unique_layers.0.pkm.kv_sel_counts_100             , 8192\n", "unique_layers.0.norm1.weight                      , 512\n", "unique_layers.0.norm1.bias                        , 512\n", "unique_layers.0.norm2.weight                      , 512\n", "unique_layers.0.norm2.bias                        , 512\n"]}], "source": ["for key in list(checkpoint['model'].keys())[:16]:\n", "    print(f\"{key:50}, {checkpoint['model'][key].numel()}\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0.7770, 0.7730, 0.7710, 0.7710, 0.7700, 0.9950, 0.9420, 0.7880, 0.8540,\n", "        0.8940, 0.7870, 0.7740, 0.8000, 0.8310, 0.7950, 0.8630, 0.8750, 0.8070,\n", "        0.8860, 0.7920, 0.8320, 0.8150, 0.9440, 0.7960, 0.8200, 0.7950, 0.8210,\n", "        0.8230, 0.8190, 0.8280, 0.8320, 0.8110, 0.8120, 0.8160, 0.7770, 0.7880,\n", "        0.7880, 0.9340, 0.7850, 0.8160, 0.8110, 0.9650, 0.7980, 0.9370, 0.8900,\n", "        0.7690, 0.9010, 0.7870, 0.9210, 0.9880, 0.9050, 0.9130, 0.8030, 0.9090,\n", "        0.7720, 0.8400, 0.7740, 0.9440, 0.9680, 0.8080, 0.8520, 0.8000, 0.9060,\n", "        0.7950], device='cuda:0')"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["checkpoint['model']['unique_layers.1.pkm.e_score_correction_bias']"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "# Read the Parquet file\n", "df = pd.read_parquet(\"/cm/shared/thongdt4/moeut_training_code/cache/SQUADCompletionTest/validation-00000-of-00001.parquet\")\n", "\n", "# Write to JSON Lines format\n", "df.to_json(\"/cm/shared/thongdt4/moeut_training_code/cache/SQUADCompletionTest/data/squad_validation.jsonl\", orient='records', lines=True)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "def _convert_to_jsonl(json_file: str, jsonl_file: str):\n", "    \"\"\"Convert SQuAD JSON to JSONL format\"\"\"\n", "    try:\n", "        with open(json_file, 'r') as f:\n", "            squad_data = json.load(f)\n", "\n", "        examples = []\n", "        for article in squad_data['data']:\n", "            for paragraph in article['paragraphs']:\n", "                context = paragraph['context']\n", "                for qa in paragraph['qas']:\n", "                    examples.append({\n", "                        'id': qa['id'],\n", "                        'context': context,\n", "                        'question': qa['question'],\n", "                        'answers': qa['answers']\n", "                    })\n", "\n", "        with open(jsonl_file, 'w') as f:\n", "            for example in examples:\n", "                f.write(json.dumps(example) + '\\n')\n", "\n", "    except Exception as e:\n", "        print(f\"Error converting SQuAD format: {e}\")\n", "\n", "_convert_to_jsonl(\n", "    \"/home/<USER>/moeut_training_code/cache/SQUAD/data/dev-v1.1.json\",\n", "    \"/home/<USER>/moeut_training_code/cache/SQUAD/data/dev-v1.1.jsonl\"\n", ")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Find the degree for the given field extension Q(sqrt(2), sqrt(3), sqrt(18)) over Q.</th>\n", "      <th>0</th>\n", "      <th>4</th>\n", "      <th>2</th>\n", "      <th>6</th>\n", "      <th>B</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Let p = (1, 2, 5, 4)(2, 3) in S_5 . Find the i...</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>24</td>\n", "      <td>120</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Find all zeros in the indicated finite field o...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0,1</td>\n", "      <td>0,4</td>\n", "      <td>D</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Find the degree for the given field extension Q(sqrt(2), sqrt(3), sqrt(18)) over Q.  \\\n", "0  Let p = (1, 2, 5, 4)(2, 3) in S_5 . Find the i...                                    \n", "1  Find all zeros in the indicated finite field o...                                    \n", "\n", "   0  4    2    6  B  \n", "0  8  2   24  120  C  \n", "1  0  1  0,1  0,4  D  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_csv(\"/cm/shared/thongdt4/moeut_training_code/cache/MMLU/test/abstract_algebra_test.csv\")\n", "df.head(2)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Let p = (1, 2, 5, 4)(2, 3) in S_5 . Find the index of <p> in S_5.',\n", " '8',\n", " '2',\n", " '24',\n", " '120',\n", " 'C']"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["list(df.iloc[0])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'answers': ['A', 'D', 'D'],\n", " 'options': [[\"puts emphasis on people's thoughts\",\n", "   'needs people to be rich in knowledge',\n", "   \"stresses more about people's identity\",\n", "   'allows people to discuss politics secretly'],\n", "  ['what is said online is under control of the Internet',\n", "   \"it is hard to protect the other's identity\",\n", "   'the faceless communication is exciting',\n", "   'one may not show the real self in cyberspace'],\n", "  ['the Internet allows people to get more information about their loved ones',\n", "   'the Internet makes it easy for people to imagine how others view them',\n", "   'people usually get to know each other by chance through the Internet',\n", "   'people may be disappointed when they meet in person']],\n", " 'questions': ['According to the passage, chatting in the cyberspace   _  .',\n", "  'People who are against online love think   _  .',\n", "  'By saying \"With so many unknowns, it\\'s easy to let one\\'s imagination \\'fill in the blanks\\'\", the writer means that   _  .'],\n", " 'article': 'Cyberspace, the connections between computers in different places, considered as a real place where information, messages and pictures exist, mirrors the real world in many ways. People ask for information, play games, and share hobbies. Others buy and sell products. Still others look for friendship, or even love.\\nUnlike the real world, however, your knowledge about a person is limited to words on a computer screen. Identity and appearance mean very little in cyberspace. Rather, a person\\'s thoughts--or at least the thoughts they type--are what really count. So even the shyest person can become a chat room star.\\nUsually, this \"faceless\" communication doesn\\'t create problems. Identity doesn\\'t really matter when you\\'re in a chat room discussing politics or hobbies. In fact, this emphasis(,) on the ideas themselves makes the Internet a great place for exciting conversation. Where else can so many people come together to chat? But some Internet users want more than just someone to chat with. They\\'re looking for serious love relationships. Is cyberspace a good place to find love? That answer depends on whom you ask. Some of these relationships actually succeed. Others fail miserably.\\nSupporters of online relationships state that the Internet allows couples to get to know each other intellectually first. Personal appearance doesn\\'t get in the way. But critics of online relationships argue that no one can truly know another person in cyberspace. Why? Because the Internet gives users a lot of control over how others view them. Internet users can carefully craft their words to fit whatever image they want to give. And they don\\'t have to worry about what their \"nonverbal\"  communication is doing for their image. In a sense, they\\'re not really themselves.\\nAll of this may be fine if the relationship stays in cyberspace. But not knowing a person is a big problem in a love relationship. With so many unknowns, it\\'s easy to let one\\'s imagination \"fill in the blanks.\" This inevitably  leads to disappointment when couples meet in person. How someone imagines an online friend is often quite more different than the real person. So, before looking for love in cyberspace, remember the advice of Internet pioneer Clifford Stoll: \"Life in the real world is far richer than anything you\\'ll find on a computer screen.\"',\n", " 'id': 'high321.txt'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "\n", "with open(\"/cm/shared/thongdt4/moeut_training_code/cache/RACE/RACE/test/high/321.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    data = json.load(f)\n", "data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test ARC-Challenge"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import json\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>questionID</th>\n", "      <th>originalQuestionID</th>\n", "      <th>totalPossiblePoint</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>isMultipleChoiceQuestion</th>\n", "      <th>includesDiagram</th>\n", "      <th>examName</th>\n", "      <th>schoolGrade</th>\n", "      <th>year</th>\n", "      <th>question</th>\n", "      <th>subject</th>\n", "      <th>category</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Mercury_7175875</td>\n", "      <td>7175875</td>\n", "      <td>1</td>\n", "      <td>C</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Mercury</td>\n", "      <td>9</td>\n", "      <td>2015</td>\n", "      <td>An astronomer observes that a planet rotates f...</td>\n", "      <td>NaN</td>\n", "      <td>Test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Mercury_SC_409171</td>\n", "      <td>409171</td>\n", "      <td>1</td>\n", "      <td>B</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Mercury</td>\n", "      <td>5</td>\n", "      <td>2015</td>\n", "      <td>A group of engineers wanted to know how differ...</td>\n", "      <td>NaN</td>\n", "      <td>Test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Mercury_SC_408547</td>\n", "      <td>408547</td>\n", "      <td>1</td>\n", "      <td>C</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Mercury</td>\n", "      <td>5</td>\n", "      <td>2015</td>\n", "      <td>The end result in the process of photosynthesi...</td>\n", "      <td>NaN</td>\n", "      <td>Test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Mercury_407327</td>\n", "      <td>407327</td>\n", "      <td>1</td>\n", "      <td>D</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Mercury</td>\n", "      <td>8</td>\n", "      <td>2015</td>\n", "      <td>A physicist wants to determine the speed a car...</td>\n", "      <td>NaN</td>\n", "      <td>Test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>MCAS_2006_9_44</td>\n", "      <td>44</td>\n", "      <td>1</td>\n", "      <td>D</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>MCAS</td>\n", "      <td>9</td>\n", "      <td>2006</td>\n", "      <td>An astronaut drops a 1.0 kg object and a 5.0 k...</td>\n", "      <td>NaN</td>\n", "      <td>Test</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          questionID originalQuestionID  totalPossiblePoint AnswerKey  \\\n", "0    Mercury_7175875            7175875                   1         C   \n", "1  Mercury_SC_409171             409171                   1         B   \n", "2  Mercury_SC_408547             408547                   1         C   \n", "3     Mercury_407327             407327                   1         D   \n", "4     MCAS_2006_9_44                 44                   1         D   \n", "\n", "   isMultipleChoiceQuestion  includesDiagram examName  schoolGrade  year  \\\n", "0                         1                0  Mercury            9  2015   \n", "1                         1                0  Mercury            5  2015   \n", "2                         1                0  Mercury            5  2015   \n", "3                         1                0  Mercury            8  2015   \n", "4                         1                0     MCAS            9  2006   \n", "\n", "                                            question  subject category  \n", "0  An astronomer observes that a planet rotates f...      NaN     Test  \n", "1  A group of engineers wanted to know how differ...      NaN     Test  \n", "2  The end result in the process of photosynthesi...      NaN     Test  \n", "3  A physicist wants to determine the speed a car...      NaN     Test  \n", "4  An astronaut drops a 1.0 kg object and a 5.0 k...      NaN     Test  "]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["df_arc = pd.read_csv(\"/home/<USER>/moeut_training_code/cache/AI2ARC/ARC-V1-Feb2018-2/ARC-Challenge/ARC-Challenge-Test.csv\")\n", "df_arc = df_arc[df_arc[\"AnswerKey\"].isin([\"A\", \"B\", \"C\", \"D\"])]\n", "df_arc.head(5)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["<PERSON><PERSON><PERSON>\n", "C    303\n", "B    301\n", "D    283\n", "A    263\n", "Name: count, dtype: int64"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["count_anwer_key = df_arc[\"AnswerKey\"].value_counts()\n", "count_anwer_key"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for -: 'str' and 'str'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[33], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28mint\u001b[39m(\u001b[43mdf_arc\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m-\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mA\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m)\n", "\u001b[0;31mTypeError\u001b[0m: unsupported operand type(s) for -: 'str' and 'str'"]}], "source": ["int(df_arc['<PERSON><PERSON><PERSON>'][0] - 'A')"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["(array(['1', '2'], dtype='<U1'), array([628, 639]))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# load /home/<USER>/moeut_training_code/cache/Winogrande/winogrande_1.1/dev-labels.lst\n", "\n", "with open(\"/home/<USER>/moeut_training_code/cache/Winogrande/winogrande_1.1/dev-labels.lst\", \"r\") as f:\n", "    labels = f.read().splitlines()\n", "    \n", "# statistics of labels\n", "np.unique(labels, return_counts=True)"]}], "metadata": {"kernelspec": {"display_name": "moeut", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}
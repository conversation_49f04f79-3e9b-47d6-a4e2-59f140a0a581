echo "Training ..."

export MOE_TYPE="competesmoe"
export CUDA_VISIBLE_DEVICES="0,1,2,3,4,5,6,7,8"
export MASTER_PORT=29531
NUM_DEVICES=$(echo $CUDA_VISIBLE_DEVICES | tr ',' '\n' | wc -l)


# Run the torchrun command and check for errors
torchrun --master_port $MASTER_PORT --nproc_per_node=$NUM_DEVICES run.py \
    /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/660M/slimpajama_moe_no_attmoe_660M_sigmoid_standardlb_nam.yaml

    # /cm/archive/thongdt4/moeut_training_code/sweeps/experiments/proposed_method/660M/slimpajama_competesmoe_no_attmoe_660M_standardlb.yaml
{"val/loss": 2.376746525840154, "val/accuracy": 0.5096232096354166, "val/perplexity": 10.769806515153912, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.4334676991338315, "lambada/accuracy/total": 0.328027950310559, "lambada/accuracy/openai_last_token": 0.7895962732919255, "lambada/perplexity": 7.566114164127005, "lambada/lm_loss": 2.9740132798897045, "lambada/lm_perplexity": 19.57030330763765, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.4188255799729878, "mean_loss": 2.4051071124869927, "blimp/accuracy/passive_2": 0.917, "blimp/accuracy/determiner_noun_agreement_2": 0.983, "blimp/accuracy/ellipsis_n_bar_1": 0.88, "blimp/accuracy/tough_vs_raising_2": 0.914, "blimp/accuracy/tough_vs_raising_1": 0.61, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.893, "blimp/accuracy/principle_A_reconstruction": 0.378, "blimp/accuracy/wh_vs_that_with_gap": 0.512, "blimp/accuracy/principle_A_domain_2": 0.875, "blimp/accuracy/determiner_noun_agreement_1": 0.993, "blimp/accuracy/ellipsis_n_bar_2": 0.875, "blimp/accuracy/principle_A_domain_3": 0.607, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.911, "blimp/accuracy/animate_subject_trans": 0.897, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.906, "blimp/accuracy/distractor_agreement_relative_clause": 0.722, "blimp/accuracy/transitive": 0.884, "blimp/accuracy/sentential_subject_island": 0.318, "blimp/accuracy/adjunct_island": 0.882, "blimp/accuracy/intransitive": 0.818, "blimp/accuracy/existential_there_subject_raising": 0.879, "blimp/accuracy/irregular_past_participle_adjectives": 0.954, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.708, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.353, "blimp/accuracy/only_npi_scope": 0.738, "blimp/accuracy/superlative_quantifiers_2": 0.783, "blimp/accuracy/passive_1": 0.91, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.918, "blimp/accuracy/inchoative": 0.682, "blimp/accuracy/anaphor_gender_agreement": 0.972, "blimp/accuracy/principle_A_c_command": 0.696, "blimp/accuracy/only_npi_licensor_present": 0.57, "blimp/accuracy/expletive_it_object_raising": 0.773, "blimp/accuracy/left_branch_island_simple_question": 0.741, "blimp/accuracy/wh_questions_subject_gap": 0.929, "blimp/accuracy/existential_there_quantifiers_2": 0.521, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.944, "blimp/accuracy/sentential_negation_npi_scope": 0.729, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.859, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.89, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.9, "blimp/accuracy/principle_A_case_2": 0.954, "blimp/accuracy/distractor_agreement_relational_noun": 0.903, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.997, "blimp/accuracy/superlative_quantifiers_1": 0.836, "blimp/accuracy/wh_island": 0.821, "blimp/accuracy/principle_A_domain_1": 0.986, "blimp/accuracy/complex_NP_island": 0.542, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.957, "blimp/accuracy/irregular_past_participle_verbs": 0.909, "blimp/accuracy/drop_argument": 0.772, "blimp/accuracy/wh_questions_object_gap": 0.84, "blimp/accuracy/animate_subject_passive": 0.798, "blimp/accuracy/existential_there_quantifiers_1": 0.985, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.898, "blimp/accuracy/npi_present_2": 0.538, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.947, "blimp/accuracy/anaphor_number_agreement": 0.992, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.964, "blimp/accuracy/existential_there_object_raising": 0.855, "blimp/accuracy/matrix_question_npi_licensor_present": 0.338, "blimp/accuracy/npi_present_1": 0.454, "blimp/accuracy/wh_vs_that_no_gap": 0.975, "blimp/accuracy/left_branch_island_echo_question": 0.429, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.963, "blimp/accuracy/causative": 0.764, "blimp/accuracy/group_average": 0.8006119402985075, "blimp/accuracy/seq_average": 0.8006119402985075, "cbt/accuracy/NE": 0.811698717948718, "cbt/accuracy/V": 0.9316, "cbt/accuracy/CN": 0.8748, "cbt/accuracy/P": 0.914, "cbt/accuracy/group_average": 0.8830246794871796, "cbt/accuracy/seq_average": 0.8830532212885154, "hellaswag/accuracy/val": 0.3428599880501892, "hellaswag/accuracy/group_average": 0.3428599880501892, "hellaswag/accuracy/seq_average": 0.3428599880501892, "piqa/accuracy/val": 0.6289445048966268, "piqa/accuracy/group_average": 0.6289445048966268, "piqa/accuracy/seq_average": 0.6289445048966268, "ai2arc/accuracy/ARC-Easy": 0.3704016913319239, "ai2arc/accuracy/ARC-Challenge": 0.23004291845493563, "ai2arc/accuracy/group_average": 0.30022230489342977, "ai2arc/accuracy/seq_average": 0.3240793201133145, "mmlu/accuracy/MMLU": 0.26864497676081517, "mmlu/accuracy/group_average": 0.26864497676081517, "mmlu/accuracy/seq_average": 0.26864497676081517, "openbookqa/accuracy/test": 0.268, "openbookqa/accuracy/group_average": 0.268, "openbookqa/accuracy/seq_average": 0.268, "race/accuracy/test/high": 0.2830188679245283, "race/accuracy/test/middle": 0.3607242339832869, "race/accuracy/group_average": 0.3218715509539076, "race/accuracy/seq_average": 0.3056343737332793, "siqa/accuracy/dev": 0.3766632548618219, "siqa/accuracy/group_average": 0.3766632548618219, "siqa/accuracy/seq_average": 0.3766632548618219, "winogrande/accuracy/dev": 0.4988161010260458, "winogrande/accuracy/group_average": 0.4988161010260458, "winogrande/accuracy/seq_average": 0.4988161010260458, "commonsenseqa/accuracy/dev_rand_split": 0.26617526617526616, "commonsenseqa/accuracy/group_average": 0.26617526617526616, "commonsenseqa/accuracy/seq_average": 0.26617526617526616}
2025-02-21 16:24:29.852333 ithndgx005: main.py -task slimpajama_transformer -test_interval 10000 -state_size 512 -transformer.encoder_n_layers 16 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.1 -amp 1 -save_interval 10000 -transformer.variant preln_moe -stop_after 100000 -moe.n_experts 64 -moe.expert_size 128 -pkm.n_heads 6 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lmds.valid_ratio 0.005 -transformer.head_projection_size 82 -transformer.universal.group_size 16 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -name slimpajama_moe_no_attmoe_154M_deepseek_highlb_shared_only
2025-02-21 16:26:39.739471 ithndgx005: main.py -task slimpajama_transformer -test_interval 10000 -state_size 512 -transformer.encoder_n_layers 16 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.1 -amp 1 -save_interval 10000 -transformer.variant preln_moe -stop_after 100000 -moe.n_experts 64 -moe.expert_size 128 -pkm.n_heads 6 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lmds.valid_ratio 0.005 -transformer.head_projection_size 82 -transformer.universal.group_size 16 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -name slimpajama_moe_no_attmoe_154M_deepseek_highlb_shared_only

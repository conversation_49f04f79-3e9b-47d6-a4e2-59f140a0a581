{"val/loss": 2.643279787093874, "val/accuracy": 0.4750879681299603, "val/perplexity": 14.059239384576212, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.6699582686335406, "lambada/accuracy/total": 0.23330745341614906, "lambada/accuracy/openai_last_token": 0.7567934782608695, "lambada/perplexity": 13.29118993486964, "lambada/lm_loss": 3.2005790199299353, "lambada/lm_perplexity": 24.54673913425713, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.3541977107730547, "mean_loss": 2.656619027863707, "blimp/accuracy/passive_2": 0.906, "blimp/accuracy/determiner_noun_agreement_2": 0.978, "blimp/accuracy/ellipsis_n_bar_1": 0.787, "blimp/accuracy/tough_vs_raising_2": 0.883, "blimp/accuracy/tough_vs_raising_1": 0.571, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.926, "blimp/accuracy/principle_A_reconstruction": 0.247, "blimp/accuracy/wh_vs_that_with_gap": 0.514, "blimp/accuracy/principle_A_domain_2": 0.829, "blimp/accuracy/determiner_noun_agreement_1": 0.989, "blimp/accuracy/ellipsis_n_bar_2": 0.899, "blimp/accuracy/principle_A_domain_3": 0.559, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.916, "blimp/accuracy/animate_subject_trans": 0.889, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.904, "blimp/accuracy/distractor_agreement_relative_clause": 0.662, "blimp/accuracy/transitive": 0.851, "blimp/accuracy/sentential_subject_island": 0.374, "blimp/accuracy/adjunct_island": 0.816, "blimp/accuracy/intransitive": 0.792, "blimp/accuracy/existential_there_subject_raising": 0.855, "blimp/accuracy/irregular_past_participle_adjectives": 0.909, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.366, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.227, "blimp/accuracy/only_npi_scope": 0.817, "blimp/accuracy/superlative_quantifiers_2": 0.663, "blimp/accuracy/passive_1": 0.89, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.892, "blimp/accuracy/inchoative": 0.632, "blimp/accuracy/anaphor_gender_agreement": 0.958, "blimp/accuracy/principle_A_c_command": 0.639, "blimp/accuracy/only_npi_licensor_present": 0.652, "blimp/accuracy/expletive_it_object_raising": 0.766, "blimp/accuracy/left_branch_island_simple_question": 0.437, "blimp/accuracy/wh_questions_subject_gap": 0.93, "blimp/accuracy/existential_there_quantifiers_2": 0.467, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.93, "blimp/accuracy/sentential_negation_npi_scope": 0.567, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.796, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.906, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.9, "blimp/accuracy/principle_A_case_2": 0.955, "blimp/accuracy/distractor_agreement_relational_noun": 0.827, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.996, "blimp/accuracy/superlative_quantifiers_1": 0.605, "blimp/accuracy/wh_island": 0.799, "blimp/accuracy/principle_A_domain_1": 0.989, "blimp/accuracy/complex_NP_island": 0.52, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.967, "blimp/accuracy/irregular_past_participle_verbs": 0.897, "blimp/accuracy/drop_argument": 0.758, "blimp/accuracy/wh_questions_object_gap": 0.803, "blimp/accuracy/animate_subject_passive": 0.808, "blimp/accuracy/existential_there_quantifiers_1": 0.987, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.875, "blimp/accuracy/npi_present_2": 0.553, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.928, "blimp/accuracy/anaphor_number_agreement": 0.981, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.953, "blimp/accuracy/existential_there_object_raising": 0.847, "blimp/accuracy/matrix_question_npi_licensor_present": 0.186, "blimp/accuracy/npi_present_1": 0.449, "blimp/accuracy/wh_vs_that_no_gap": 0.967, "blimp/accuracy/left_branch_island_echo_question": 0.443, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.975, "blimp/accuracy/causative": 0.7, "blimp/accuracy/group_average": 0.7650597014925372, "blimp/accuracy/seq_average": 0.7650597014925373, "cbt/accuracy/NE": 0.7455929487179487, "cbt/accuracy/V": 0.9108, "cbt/accuracy/CN": 0.8072, "cbt/accuracy/P": 0.8804, "cbt/accuracy/group_average": 0.8359982371794872, "cbt/accuracy/seq_average": 0.8360344137655062, "hellaswag/accuracy/val": 0.2878908583947421, "hellaswag/accuracy/group_average": 0.2878908583947421, "hellaswag/accuracy/seq_average": 0.2878908583947421, "piqa/accuracy/val": 0.6017410228509249, "piqa/accuracy/group_average": 0.6017410228509249, "piqa/accuracy/seq_average": 0.6017410228509249, "ai2arc/accuracy/ARC-Easy": 0.3315010570824524, "ai2arc/accuracy/ARC-Challenge": 0.2034334763948498, "ai2arc/accuracy/group_average": 0.2674672667386511, "ai2arc/accuracy/seq_average": 0.2892351274787535, "mmlu/accuracy/MMLU": 0.2627815516624955, "mmlu/accuracy/group_average": 0.2627815516624955, "mmlu/accuracy/seq_average": 0.2627815516624955, "openbookqa/accuracy/test": 0.268, "openbookqa/accuracy/group_average": 0.268, "openbookqa/accuracy/seq_average": 0.268, "race/accuracy/test/high": 0.2715837621497999, "race/accuracy/test/middle": 0.34331476323119775, "race/accuracy/group_average": 0.3074492626904988, "race/accuracy/seq_average": 0.2924604783137414, "siqa/accuracy/dev": 0.34646878198567044, "siqa/accuracy/group_average": 0.34646878198567044, "siqa/accuracy/seq_average": 0.34646878198567044, "winogrande/accuracy/dev": 0.5169692186266772, "winogrande/accuracy/group_average": 0.5169692186266772, "winogrande/accuracy/seq_average": 0.5169692186266772, "commonsenseqa/accuracy/dev_rand_split": 0.24488124488124488, "commonsenseqa/accuracy/group_average": 0.24488124488124488, "commonsenseqa/accuracy/seq_average": 0.24488124488124488}
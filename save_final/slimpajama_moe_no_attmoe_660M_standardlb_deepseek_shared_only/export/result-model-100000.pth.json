{"val/loss": 2.4443790496341764, "val/accuracy": 0.4993257068452381, "val/perplexity": 11.523391922358208, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.501361159804445, "lambada/accuracy/total": 0.3159937888198758, "lambada/accuracy/openai_last_token": 0.7845496894409938, "lambada/perplexity": 8.75451786621247, "lambada/lm_loss": 3.027924854849423, "lambada/lm_perplexity": 20.65432736023701, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.40765974783255693, "mean_loss": 2.472870104719311, "blimp/accuracy/passive_2": 0.885, "blimp/accuracy/determiner_noun_agreement_2": 0.983, "blimp/accuracy/ellipsis_n_bar_1": 0.849, "blimp/accuracy/tough_vs_raising_2": 0.881, "blimp/accuracy/tough_vs_raising_1": 0.65, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.919, "blimp/accuracy/principle_A_reconstruction": 0.415, "blimp/accuracy/wh_vs_that_with_gap": 0.532, "blimp/accuracy/principle_A_domain_2": 0.884, "blimp/accuracy/determiner_noun_agreement_1": 0.991, "blimp/accuracy/ellipsis_n_bar_2": 0.91, "blimp/accuracy/principle_A_domain_3": 0.681, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.948, "blimp/accuracy/animate_subject_trans": 0.914, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.917, "blimp/accuracy/distractor_agreement_relative_clause": 0.643, "blimp/accuracy/transitive": 0.895, "blimp/accuracy/sentential_subject_island": 0.252, "blimp/accuracy/adjunct_island": 0.86, "blimp/accuracy/intransitive": 0.798, "blimp/accuracy/existential_there_subject_raising": 0.863, "blimp/accuracy/irregular_past_participle_adjectives": 0.886, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.558, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.336, "blimp/accuracy/only_npi_scope": 0.649, "blimp/accuracy/superlative_quantifiers_2": 0.845, "blimp/accuracy/passive_1": 0.898, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.898, "blimp/accuracy/inchoative": 0.653, "blimp/accuracy/anaphor_gender_agreement": 0.975, "blimp/accuracy/principle_A_c_command": 0.72, "blimp/accuracy/only_npi_licensor_present": 0.539, "blimp/accuracy/expletive_it_object_raising": 0.805, "blimp/accuracy/left_branch_island_simple_question": 0.632, "blimp/accuracy/wh_questions_subject_gap": 0.923, "blimp/accuracy/existential_there_quantifiers_2": 0.504, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.951, "blimp/accuracy/sentential_negation_npi_scope": 0.754, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.82, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.903, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.92, "blimp/accuracy/principle_A_case_2": 0.937, "blimp/accuracy/distractor_agreement_relational_noun": 0.887, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.984, "blimp/accuracy/superlative_quantifiers_1": 0.625, "blimp/accuracy/wh_island": 0.731, "blimp/accuracy/principle_A_domain_1": 0.97, "blimp/accuracy/complex_NP_island": 0.569, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.972, "blimp/accuracy/irregular_past_participle_verbs": 0.918, "blimp/accuracy/drop_argument": 0.753, "blimp/accuracy/wh_questions_object_gap": 0.848, "blimp/accuracy/animate_subject_passive": 0.805, "blimp/accuracy/existential_there_quantifiers_1": 0.97, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.904, "blimp/accuracy/npi_present_2": 0.665, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.959, "blimp/accuracy/anaphor_number_agreement": 0.99, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.957, "blimp/accuracy/existential_there_object_raising": 0.855, "blimp/accuracy/matrix_question_npi_licensor_present": 0.361, "blimp/accuracy/npi_present_1": 0.645, "blimp/accuracy/wh_vs_that_no_gap": 0.973, "blimp/accuracy/left_branch_island_echo_question": 0.534, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.964, "blimp/accuracy/causative": 0.752, "blimp/accuracy/group_average": 0.7965223880597015, "blimp/accuracy/seq_average": 0.7965223880597015, "cbt/accuracy/NE": 0.7760416666666666, "cbt/accuracy/V": 0.9288, "cbt/accuracy/CN": 0.8656, "cbt/accuracy/P": 0.9072, "cbt/accuracy/group_average": 0.8694104166666666, "cbt/accuracy/seq_average": 0.8694477791116446, "hellaswag/accuracy/val": 0.3253335988846843, "hellaswag/accuracy/group_average": 0.3253335988846843, "hellaswag/accuracy/seq_average": 0.3253335988846843, "piqa/accuracy/val": 0.6196953210010882, "piqa/accuracy/group_average": 0.6196953210010882, "piqa/accuracy/seq_average": 0.6196953210010882, "ai2arc/accuracy/ARC-Easy": 0.3572938689217759, "ai2arc/accuracy/ARC-Challenge": 0.22489270386266094, "ai2arc/accuracy/group_average": 0.29109328639221843, "ai2arc/accuracy/seq_average": 0.31359773371104815, "mmlu/accuracy/MMLU": 0.25956381837683234, "mmlu/accuracy/group_average": 0.25956381837683234, "mmlu/accuracy/seq_average": 0.25956381837683234, "openbookqa/accuracy/test": 0.286, "openbookqa/accuracy/group_average": 0.286, "openbookqa/accuracy/seq_average": 0.286, "race/accuracy/test/high": 0.27930245854774155, "race/accuracy/test/middle": 0.3544568245125348, "race/accuracy/group_average": 0.31687964153013815, "race/accuracy/seq_average": 0.30117551682205107, "siqa/accuracy/dev": 0.36284544524053225, "siqa/accuracy/group_average": 0.36284544524053225, "siqa/accuracy/seq_average": 0.36284544524053225, "winogrande/accuracy/dev": 0.5090765588003157, "winogrande/accuracy/group_average": 0.5090765588003157, "winogrande/accuracy/seq_average": 0.5090765588003157, "commonsenseqa/accuracy/dev_rand_split": 0.2628992628992629, "commonsenseqa/accuracy/group_average": 0.2628992628992629, "commonsenseqa/accuracy/seq_average": 0.2628992628992629}
{"val/loss": 2.3456648569258434, "val/accuracy": 0.5138578869047619, "val/perplexity": 10.440211667626619, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.4847658524602094, "lambada/accuracy/total": 0.33773291925465837, "lambada/accuracy/openai_last_token": 0.7927018633540373, "lambada/perplexity": 7.200324526814876, "lambada/lm_loss": 2.952690654838108, "lambada/lm_perplexity": 19.157430477092852, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.4257954030797101, "mean_loss": 2.4152153546930264, "blimp/accuracy/passive_2": 0.919, "blimp/accuracy/determiner_noun_agreement_2": 0.98, "blimp/accuracy/ellipsis_n_bar_1": 0.819, "blimp/accuracy/tough_vs_raising_2": 0.88, "blimp/accuracy/tough_vs_raising_1": 0.61, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.933, "blimp/accuracy/principle_A_reconstruction": 0.535, "blimp/accuracy/wh_vs_that_with_gap": 0.48, "blimp/accuracy/principle_A_domain_2": 0.866, "blimp/accuracy/determiner_noun_agreement_1": 0.996, "blimp/accuracy/ellipsis_n_bar_2": 0.896, "blimp/accuracy/principle_A_domain_3": 0.65, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.932, "blimp/accuracy/animate_subject_trans": 0.896, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.921, "blimp/accuracy/distractor_agreement_relative_clause": 0.728, "blimp/accuracy/transitive": 0.876, "blimp/accuracy/sentential_subject_island": 0.332, "blimp/accuracy/adjunct_island": 0.861, "blimp/accuracy/intransitive": 0.753, "blimp/accuracy/existential_there_subject_raising": 0.877, "blimp/accuracy/irregular_past_participle_adjectives": 0.965, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.674, "blimp/accuracy/principle_A_case_1": 0.999, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.328, "blimp/accuracy/only_npi_scope": 0.611, "blimp/accuracy/superlative_quantifiers_2": 0.823, "blimp/accuracy/passive_1": 0.895, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.914, "blimp/accuracy/inchoative": 0.629, "blimp/accuracy/anaphor_gender_agreement": 0.977, "blimp/accuracy/principle_A_c_command": 0.757, "blimp/accuracy/only_npi_licensor_present": 0.828, "blimp/accuracy/expletive_it_object_raising": 0.819, "blimp/accuracy/left_branch_island_simple_question": 0.763, "blimp/accuracy/wh_questions_subject_gap": 0.94, "blimp/accuracy/existential_there_quantifiers_2": 0.416, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.947, "blimp/accuracy/sentential_negation_npi_scope": 0.683, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.84, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.926, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.922, "blimp/accuracy/principle_A_case_2": 0.936, "blimp/accuracy/distractor_agreement_relational_noun": 0.908, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.976, "blimp/accuracy/superlative_quantifiers_1": 0.772, "blimp/accuracy/wh_island": 0.737, "blimp/accuracy/principle_A_domain_1": 0.991, "blimp/accuracy/complex_NP_island": 0.58, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.975, "blimp/accuracy/irregular_past_participle_verbs": 0.918, "blimp/accuracy/drop_argument": 0.714, "blimp/accuracy/wh_questions_object_gap": 0.867, "blimp/accuracy/animate_subject_passive": 0.782, "blimp/accuracy/existential_there_quantifiers_1": 0.973, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.893, "blimp/accuracy/npi_present_2": 0.614, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.965, "blimp/accuracy/anaphor_number_agreement": 0.99, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.974, "blimp/accuracy/existential_there_object_raising": 0.864, "blimp/accuracy/matrix_question_npi_licensor_present": 0.337, "blimp/accuracy/npi_present_1": 0.505, "blimp/accuracy/wh_vs_that_no_gap": 0.979, "blimp/accuracy/left_branch_island_echo_question": 0.555, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.953, "blimp/accuracy/causative": 0.781, "blimp/accuracy/group_average": 0.8049999999999999, "blimp/accuracy/seq_average": 0.805, "cbt/accuracy/NE": 0.811698717948718, "cbt/accuracy/V": 0.9352, "cbt/accuracy/CN": 0.8808, "cbt/accuracy/P": 0.9212, "cbt/accuracy/group_average": 0.8872246794871794, "cbt/accuracy/seq_average": 0.8872549019607843, "hellaswag/accuracy/val": 0.3483369846644095, "hellaswag/accuracy/group_average": 0.3483369846644095, "hellaswag/accuracy/seq_average": 0.3483369846644095, "piqa/accuracy/val": 0.6284004352557128, "piqa/accuracy/group_average": 0.6284004352557128, "piqa/accuracy/seq_average": 0.6284004352557128, "ai2arc/accuracy/ARC-Easy": 0.3704016913319239, "ai2arc/accuracy/ARC-Challenge": 0.2446351931330472, "ai2arc/accuracy/group_average": 0.3075184422324856, "ai2arc/accuracy/seq_average": 0.32889518413597735, "mmlu/accuracy/MMLU": 0.26106542724347515, "mmlu/accuracy/group_average": 0.26106542724347515, "mmlu/accuracy/seq_average": 0.26106542724347515, "openbookqa/accuracy/test": 0.294, "openbookqa/accuracy/group_average": 0.294, "openbookqa/accuracy/seq_average": 0.294, "race/accuracy/test/high": 0.28244711263579186, "race/accuracy/test/middle": 0.35863509749303624, "race/accuracy/group_average": 0.320541105064414, "race/accuracy/seq_average": 0.3046209971625456, "siqa/accuracy/dev": 0.372057318321392, "siqa/accuracy/group_average": 0.372057318321392, "siqa/accuracy/seq_average": 0.372057318321392, "winogrande/accuracy/dev": 0.5130228887134964, "winogrande/accuracy/group_average": 0.5130228887134964, "winogrande/accuracy/seq_average": 0.5130228887134964, "commonsenseqa/accuracy/dev_rand_split": 0.27436527436527436, "commonsenseqa/accuracy/group_average": 0.27436527436527436, "commonsenseqa/accuracy/seq_average": 0.27436527436527436}
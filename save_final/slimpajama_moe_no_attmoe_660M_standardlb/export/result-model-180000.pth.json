{"val/loss": 2.3594217451791915, "val/accuracy": 0.5116422138516865, "val/perplexity": 10.584828954052167, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.3822523792337926, "lambada/accuracy/total": 0.35229037267080743, "lambada/accuracy/openai_last_token": 0.797748447204969, "lambada/perplexity": 7.606355550412874, "lambada/lm_loss": 2.9619059617024575, "lambada/lm_perplexity": 19.3347880246582, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.43196629326124697, "mean_loss": 2.3708370622064923, "blimp/accuracy/passive_2": 0.917, "blimp/accuracy/determiner_noun_agreement_2": 0.987, "blimp/accuracy/ellipsis_n_bar_1": 0.854, "blimp/accuracy/tough_vs_raising_2": 0.859, "blimp/accuracy/tough_vs_raising_1": 0.64, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.918, "blimp/accuracy/principle_A_reconstruction": 0.288, "blimp/accuracy/wh_vs_that_with_gap": 0.465, "blimp/accuracy/principle_A_domain_2": 0.892, "blimp/accuracy/determiner_noun_agreement_1": 0.989, "blimp/accuracy/ellipsis_n_bar_2": 0.905, "blimp/accuracy/principle_A_domain_3": 0.61, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.945, "blimp/accuracy/animate_subject_trans": 0.901, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.918, "blimp/accuracy/distractor_agreement_relative_clause": 0.678, "blimp/accuracy/transitive": 0.906, "blimp/accuracy/sentential_subject_island": 0.292, "blimp/accuracy/adjunct_island": 0.868, "blimp/accuracy/intransitive": 0.751, "blimp/accuracy/existential_there_subject_raising": 0.889, "blimp/accuracy/irregular_past_participle_adjectives": 0.956, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.767, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.339, "blimp/accuracy/only_npi_scope": 0.709, "blimp/accuracy/superlative_quantifiers_2": 0.747, "blimp/accuracy/passive_1": 0.892, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.932, "blimp/accuracy/inchoative": 0.606, "blimp/accuracy/anaphor_gender_agreement": 0.967, "blimp/accuracy/principle_A_c_command": 0.687, "blimp/accuracy/only_npi_licensor_present": 0.546, "blimp/accuracy/expletive_it_object_raising": 0.761, "blimp/accuracy/left_branch_island_simple_question": 0.854, "blimp/accuracy/wh_questions_subject_gap": 0.937, "blimp/accuracy/existential_there_quantifiers_2": 0.537, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.936, "blimp/accuracy/sentential_negation_npi_scope": 0.725, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.811, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.934, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.894, "blimp/accuracy/principle_A_case_2": 0.931, "blimp/accuracy/distractor_agreement_relational_noun": 0.828, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.98, "blimp/accuracy/superlative_quantifiers_1": 0.685, "blimp/accuracy/wh_island": 0.774, "blimp/accuracy/principle_A_domain_1": 0.994, "blimp/accuracy/complex_NP_island": 0.608, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.977, "blimp/accuracy/irregular_past_participle_verbs": 0.899, "blimp/accuracy/drop_argument": 0.754, "blimp/accuracy/wh_questions_object_gap": 0.847, "blimp/accuracy/animate_subject_passive": 0.794, "blimp/accuracy/existential_there_quantifiers_1": 0.975, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.899, "blimp/accuracy/npi_present_2": 0.555, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.931, "blimp/accuracy/anaphor_number_agreement": 0.989, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.956, "blimp/accuracy/existential_there_object_raising": 0.853, "blimp/accuracy/matrix_question_npi_licensor_present": 0.367, "blimp/accuracy/npi_present_1": 0.561, "blimp/accuracy/wh_vs_that_no_gap": 0.978, "blimp/accuracy/left_branch_island_echo_question": 0.508, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.961, "blimp/accuracy/causative": 0.755, "blimp/accuracy/group_average": 0.7965373134328356, "blimp/accuracy/seq_average": 0.7965373134328358, "cbt/accuracy/NE": 0.8044871794871795, "cbt/accuracy/V": 0.934, "cbt/accuracy/CN": 0.8792, "cbt/accuracy/P": 0.9168, "cbt/accuracy/group_average": 0.8836217948717948, "cbt/accuracy/seq_average": 0.8836534613845538, "hellaswag/accuracy/val": 0.34734116709818763, "hellaswag/accuracy/group_average": 0.34734116709818763, "hellaswag/accuracy/seq_average": 0.34734116709818763, "piqa/accuracy/val": 0.6327529923830251, "piqa/accuracy/group_average": 0.6327529923830251, "piqa/accuracy/seq_average": 0.6327529923830251, "ai2arc/accuracy/ARC-Easy": 0.3788583509513742, "ai2arc/accuracy/ARC-Challenge": 0.22660944206008585, "ai2arc/accuracy/group_average": 0.30273389650573, "ai2arc/accuracy/seq_average": 0.3286118980169972, "mmlu/accuracy/MMLU": 0.25927779764032893, "mmlu/accuracy/group_average": 0.25927779764032893, "mmlu/accuracy/seq_average": 0.25927779764032893, "openbookqa/accuracy/test": 0.28, "openbookqa/accuracy/group_average": 0.28, "openbookqa/accuracy/seq_average": 0.28, "race/accuracy/test/high": 0.29130931961120643, "race/accuracy/test/middle": 0.36768802228412256, "race/accuracy/group_average": 0.3294986709476645, "race/accuracy/seq_average": 0.313538710985002, "siqa/accuracy/dev": 0.3705220061412487, "siqa/accuracy/group_average": 0.3705220061412487, "siqa/accuracy/seq_average": 0.3705220061412487, "winogrande/accuracy/dev": 0.4988161010260458, "winogrande/accuracy/group_average": 0.4988161010260458, "winogrande/accuracy/seq_average": 0.4988161010260458, "commonsenseqa/accuracy/dev_rand_split": 0.26453726453726456, "commonsenseqa/accuracy/group_average": 0.26453726453726456, "commonsenseqa/accuracy/seq_average": 0.26453726453726456}
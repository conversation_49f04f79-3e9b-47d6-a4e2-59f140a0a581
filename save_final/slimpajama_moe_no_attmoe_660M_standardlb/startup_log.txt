2025-03-30 07:34:40.817970 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_standardlb
2025-03-30 19:56:10.549158 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-140000.pth -name slimpajama_moe_no_attmoe_660M_standardlb
2025-03-31 07:08:02.170083 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-160000.pth -name slimpajama_moe_no_attmoe_660M_standardlb
2025-04-02 20:06:30.305701 ithndgx005: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-240000.pth -name slimpajama_moe_no_attmoe_660M_standardlb
2025-04-02 20:07:53.062250 ithndgx005: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-240000.pth -name slimpajama_moe_no_attmoe_660M_standardlb
2025-04-03 06:22:33.660774 ithndgx005: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-240000.pth -name slimpajama_moe_no_attmoe_660M_standardlb
2025-04-05 10:40:48.650126 ithndgx005: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode gate -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 0,1,2,3 -restore /cm/shared/thongdt4/moeut_training_code/save/slimpajama_moe_no_attmoe_660M_standardlb/checkpoint/model-300000.pth -name slimpajama_moe_no_attmoe_660M_standardlb

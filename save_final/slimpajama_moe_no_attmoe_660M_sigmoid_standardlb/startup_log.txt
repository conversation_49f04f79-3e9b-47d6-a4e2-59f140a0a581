2025-04-09 23:10:11.033490 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode sigmoid -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_sigmoid_standardlb
2025-04-09 23:13:25.060925 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode sigmoid -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_sigmoid_standardlb
2025-04-09 23:18:09.135437 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode sigmoid -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_sigmoid_standardlb
2025-04-10 05:48:57.685218 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode sigmoid -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_sigmoid_standardlb
2025-04-10 05:53:52.299234 SPP00018465: main.py -task slimpajama_large_transformer -test_interval 20000 -state_size 1024 -transformer.encoder_n_layers 18 -transformer.n_heads 4 -dropout 0.0 -moe.drop_expert 0.0 -lr 0.00025 -optimizer adamw -lm.unroll 1024 -grad_clip 0.25 -amp 1 -save_interval 20000 -transformer.variant preln_moe -stop_after 400000 -moe.n_experts 66 -moe.expert_size 256 -pkm.n_heads 8 -transformer.p_drop_layer 0.0 -moe.selection_mode sigmoid -moe.perplexity_reg_mode standard -moe.perplexity_reg 0.01 -lr_sched.type cos -lr_warmup 4000 -lmds.valid_ratio 0.005 -transformer.head_projection_size 128 -transformer.universal.group_size 18 -wd 0.01 -lm.trafo.context_blocks 0 -min_lr_multiplier 0.1 -details_log_interval 500 -lm.eval.enabled 0 -batch_size 64 -per_device_batch_size 16 -n_microbatch None -gpu 4,5,6,7 -name slimpajama_moe_no_attmoe_660M_sigmoid_standardlb

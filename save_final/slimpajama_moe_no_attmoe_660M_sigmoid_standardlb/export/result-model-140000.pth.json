{"val/loss": 2.390654790969122, "val/accuracy": 0.507262214781746, "val/perplexity": 10.920642340415057, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.3997605602193324, "lambada/accuracy/total": 0.33656832298136646, "lambada/accuracy/openai_last_token": 0.7897903726708074, "lambada/perplexity": 7.638020823055553, "lambada/lm_loss": 2.9876965111479414, "lambada/lm_perplexity": 19.8399287604656, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.4219152688815563, "mean_loss": 2.395207675594227, "blimp/accuracy/passive_2": 0.911, "blimp/accuracy/determiner_noun_agreement_2": 0.986, "blimp/accuracy/ellipsis_n_bar_1": 0.836, "blimp/accuracy/tough_vs_raising_2": 0.905, "blimp/accuracy/tough_vs_raising_1": 0.626, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.934, "blimp/accuracy/principle_A_reconstruction": 0.479, "blimp/accuracy/wh_vs_that_with_gap": 0.554, "blimp/accuracy/principle_A_domain_2": 0.869, "blimp/accuracy/determiner_noun_agreement_1": 0.993, "blimp/accuracy/ellipsis_n_bar_2": 0.919, "blimp/accuracy/principle_A_domain_3": 0.576, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.933, "blimp/accuracy/animate_subject_trans": 0.916, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.909, "blimp/accuracy/distractor_agreement_relative_clause": 0.659, "blimp/accuracy/transitive": 0.884, "blimp/accuracy/sentential_subject_island": 0.354, "blimp/accuracy/adjunct_island": 0.891, "blimp/accuracy/intransitive": 0.784, "blimp/accuracy/existential_there_subject_raising": 0.89, "blimp/accuracy/irregular_past_participle_adjectives": 0.95, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.748, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.419, "blimp/accuracy/only_npi_scope": 0.764, "blimp/accuracy/superlative_quantifiers_2": 0.685, "blimp/accuracy/passive_1": 0.883, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.913, "blimp/accuracy/inchoative": 0.635, "blimp/accuracy/anaphor_gender_agreement": 0.965, "blimp/accuracy/principle_A_c_command": 0.623, "blimp/accuracy/only_npi_licensor_present": 0.695, "blimp/accuracy/expletive_it_object_raising": 0.785, "blimp/accuracy/left_branch_island_simple_question": 0.785, "blimp/accuracy/wh_questions_subject_gap": 0.94, "blimp/accuracy/existential_there_quantifiers_2": 0.472, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.94, "blimp/accuracy/sentential_negation_npi_scope": 0.671, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.819, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.909, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.9, "blimp/accuracy/principle_A_case_2": 0.958, "blimp/accuracy/distractor_agreement_relational_noun": 0.866, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.981, "blimp/accuracy/superlative_quantifiers_1": 0.802, "blimp/accuracy/wh_island": 0.785, "blimp/accuracy/principle_A_domain_1": 0.975, "blimp/accuracy/complex_NP_island": 0.596, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.982, "blimp/accuracy/irregular_past_participle_verbs": 0.914, "blimp/accuracy/drop_argument": 0.759, "blimp/accuracy/wh_questions_object_gap": 0.839, "blimp/accuracy/animate_subject_passive": 0.799, "blimp/accuracy/existential_there_quantifiers_1": 0.987, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.898, "blimp/accuracy/npi_present_2": 0.569, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.971, "blimp/accuracy/anaphor_number_agreement": 0.986, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.971, "blimp/accuracy/existential_there_object_raising": 0.823, "blimp/accuracy/matrix_question_npi_licensor_present": 0.358, "blimp/accuracy/npi_present_1": 0.593, "blimp/accuracy/wh_vs_that_no_gap": 0.987, "blimp/accuracy/left_branch_island_echo_question": 0.539, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.959, "blimp/accuracy/causative": 0.753, "blimp/accuracy/group_average": 0.8053582089552238, "blimp/accuracy/seq_average": 0.8053582089552239, "cbt/accuracy/NE": 0.8024839743589743, "cbt/accuracy/V": 0.9384, "cbt/accuracy/CN": 0.8728, "cbt/accuracy/P": 0.916, "cbt/accuracy/group_average": 0.8824209935897436, "cbt/accuracy/seq_average": 0.882452981192477, "hellaswag/accuracy/val": 0.3390758812985461, "hellaswag/accuracy/group_average": 0.3390758812985461, "hellaswag/accuracy/seq_average": 0.3390758812985461, "piqa/accuracy/val": 0.6251360174102285, "piqa/accuracy/group_average": 0.6251360174102285, "piqa/accuracy/seq_average": 0.6251360174102285, "ai2arc/accuracy/ARC-Easy": 0.36448202959830867, "ai2arc/accuracy/ARC-Challenge": 0.22317596566523606, "ai2arc/accuracy/group_average": 0.2938289976317724, "ai2arc/accuracy/seq_average": 0.3178470254957507, "mmlu/accuracy/MMLU": 0.26378262424025745, "mmlu/accuracy/group_average": 0.26378262424025745, "mmlu/accuracy/seq_average": 0.26378262424025745, "openbookqa/accuracy/test": 0.296, "openbookqa/accuracy/group_average": 0.296, "openbookqa/accuracy/seq_average": 0.296, "race/accuracy/test/high": 0.279874213836478, "race/accuracy/test/middle": 0.35236768802228413, "race/accuracy/group_average": 0.3161209509293811, "race/accuracy/seq_average": 0.3009728415079043, "siqa/accuracy/dev": 0.3741044012282497, "siqa/accuracy/group_average": 0.3741044012282497, "siqa/accuracy/seq_average": 0.3741044012282497, "winogrande/accuracy/dev": 0.5067087608524072, "winogrande/accuracy/group_average": 0.5067087608524072, "winogrande/accuracy/seq_average": 0.5067087608524072, "commonsenseqa/accuracy/dev_rand_split": 0.2628992628992629, "commonsenseqa/accuracy/group_average": 0.2628992628992629, "commonsenseqa/accuracy/seq_average": 0.2628992628992629}
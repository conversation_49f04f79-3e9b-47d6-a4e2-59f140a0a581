{"val/loss": 2.3537970648871527, "val/accuracy": 0.5129229833209326, "val/perplexity": 10.525459798022535, "val/time_since_best_loss": 0, "val/time_since_best_accuracy": 0, "lambada/loss": 2.545559142687306, "lambada/accuracy/total": 0.343944099378882, "lambada/accuracy/openai_last_token": 0.797166149068323, "lambada/perplexity": 7.222484004238777, "lambada/lm_loss": 2.9563118238790773, "lambada/lm_perplexity": 19.226928527391898, "lambada/time_since_best_loss": 0, "lambada/time_since_best_accuracy": 0, "mean_accuracy": 0.4284335413499073, "mean_loss": 2.449678103787229, "blimp/accuracy/passive_2": 0.912, "blimp/accuracy/determiner_noun_agreement_2": 0.976, "blimp/accuracy/ellipsis_n_bar_1": 0.844, "blimp/accuracy/tough_vs_raising_2": 0.891, "blimp/accuracy/tough_vs_raising_1": 0.598, "blimp/accuracy/irregular_plural_subject_verb_agreement_2": 0.93, "blimp/accuracy/principle_A_reconstruction": 0.328, "blimp/accuracy/wh_vs_that_with_gap": 0.483, "blimp/accuracy/principle_A_domain_2": 0.878, "blimp/accuracy/determiner_noun_agreement_1": 0.993, "blimp/accuracy/ellipsis_n_bar_2": 0.905, "blimp/accuracy/principle_A_domain_3": 0.584, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_2": 0.927, "blimp/accuracy/animate_subject_trans": 0.904, "blimp/accuracy/determiner_noun_agreement_with_adj_irregular_1": 0.926, "blimp/accuracy/distractor_agreement_relative_clause": 0.668, "blimp/accuracy/transitive": 0.903, "blimp/accuracy/sentential_subject_island": 0.33, "blimp/accuracy/adjunct_island": 0.862, "blimp/accuracy/intransitive": 0.773, "blimp/accuracy/existential_there_subject_raising": 0.881, "blimp/accuracy/irregular_past_participle_adjectives": 0.975, "blimp/accuracy/coordinate_structure_constraint_complex_left_branch": 0.684, "blimp/accuracy/principle_A_case_1": 1.0, "blimp/accuracy/wh_vs_that_with_gap_long_distance": 0.344, "blimp/accuracy/only_npi_scope": 0.778, "blimp/accuracy/superlative_quantifiers_2": 0.841, "blimp/accuracy/passive_1": 0.904, "blimp/accuracy/regular_plural_subject_verb_agreement_1": 0.947, "blimp/accuracy/inchoative": 0.619, "blimp/accuracy/anaphor_gender_agreement": 0.969, "blimp/accuracy/principle_A_c_command": 0.605, "blimp/accuracy/only_npi_licensor_present": 0.756, "blimp/accuracy/expletive_it_object_raising": 0.826, "blimp/accuracy/left_branch_island_simple_question": 0.787, "blimp/accuracy/wh_questions_subject_gap": 0.967, "blimp/accuracy/existential_there_quantifiers_2": 0.557, "blimp/accuracy/determiner_noun_agreement_with_adj_2": 0.937, "blimp/accuracy/sentential_negation_npi_scope": 0.713, "blimp/accuracy/coordinate_structure_constraint_object_extraction": 0.825, "blimp/accuracy/wh_questions_subject_gap_long_distance": 0.939, "blimp/accuracy/irregular_plural_subject_verb_agreement_1": 0.895, "blimp/accuracy/principle_A_case_2": 0.927, "blimp/accuracy/distractor_agreement_relational_noun": 0.855, "blimp/accuracy/sentential_negation_npi_licensor_present": 0.981, "blimp/accuracy/superlative_quantifiers_1": 0.786, "blimp/accuracy/wh_island": 0.788, "blimp/accuracy/principle_A_domain_1": 0.988, "blimp/accuracy/complex_NP_island": 0.578, "blimp/accuracy/determiner_noun_agreement_irregular_2": 0.976, "blimp/accuracy/irregular_past_participle_verbs": 0.873, "blimp/accuracy/drop_argument": 0.74, "blimp/accuracy/wh_questions_object_gap": 0.874, "blimp/accuracy/animate_subject_passive": 0.82, "blimp/accuracy/existential_there_quantifiers_1": 0.986, "blimp/accuracy/regular_plural_subject_verb_agreement_2": 0.891, "blimp/accuracy/npi_present_2": 0.533, "blimp/accuracy/determiner_noun_agreement_irregular_1": 0.942, "blimp/accuracy/anaphor_number_agreement": 0.992, "blimp/accuracy/determiner_noun_agreement_with_adjective_1": 0.963, "blimp/accuracy/existential_there_object_raising": 0.889, "blimp/accuracy/matrix_question_npi_licensor_present": 0.301, "blimp/accuracy/npi_present_1": 0.5, "blimp/accuracy/wh_vs_that_no_gap": 0.982, "blimp/accuracy/left_branch_island_echo_question": 0.498, "blimp/accuracy/wh_vs_that_no_gap_long_distance": 0.972, "blimp/accuracy/causative": 0.759, "blimp/accuracy/group_average": 0.8023582089552239, "blimp/accuracy/seq_average": 0.8023582089552239, "cbt/accuracy/NE": 0.8145032051282052, "cbt/accuracy/V": 0.9344, "cbt/accuracy/CN": 0.8808, "cbt/accuracy/P": 0.92, "cbt/accuracy/group_average": 0.8874258012820513, "cbt/accuracy/seq_average": 0.8874549819927972, "hellaswag/accuracy/val": 0.35012945628360886, "hellaswag/accuracy/group_average": 0.35012945628360886, "hellaswag/accuracy/seq_average": 0.35012945628360886, "piqa/accuracy/val": 0.6463547334058759, "piqa/accuracy/group_average": 0.6463547334058759, "piqa/accuracy/seq_average": 0.6463547334058759, "ai2arc/accuracy/ARC-Easy": 0.37970401691331923, "ai2arc/accuracy/ARC-Challenge": 0.22746781115879827, "ai2arc/accuracy/group_average": 0.3035859140360587, "ai2arc/accuracy/seq_average": 0.32946175637393765, "mmlu/accuracy/MMLU": 0.25942080800858064, "mmlu/accuracy/group_average": 0.25942080800858064, "mmlu/accuracy/seq_average": 0.25942080800858064, "openbookqa/accuracy/test": 0.278, "openbookqa/accuracy/group_average": 0.278, "openbookqa/accuracy/seq_average": 0.278, "race/accuracy/test/high": 0.2864493996569468, "race/accuracy/test/middle": 0.35097493036211697, "race/accuracy/group_average": 0.3187121650095319, "race/accuracy/seq_average": 0.3052290231049858, "siqa/accuracy/dev": 0.37871033776867963, "siqa/accuracy/group_average": 0.37871033776867963, "siqa/accuracy/seq_average": 0.37871033776867963, "winogrande/accuracy/dev": 0.5146014206787688, "winogrande/accuracy/group_average": 0.5146014206787688, "winogrande/accuracy/seq_average": 0.5146014206787688, "commonsenseqa/accuracy/dev_rand_split": 0.276003276003276, "commonsenseqa/accuracy/group_average": 0.276003276003276, "commonsenseqa/accuracy/seq_average": 0.276003276003276}
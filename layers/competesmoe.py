import torch
import torch.distributed
import torch.nn.functional as F
import numpy as np
from typing import Any, Dict, Tuple, List, Union, Optional
from framework.layers import LoggingLayer
from framework.layers import RegularizedLayer
from framework import utils
import framework
import math
from framework.layers import OncePerIterLayer
from layers import cvmm, cvmm_prepare_sel
from layers.cvmm import CVMMSel, cvmm_prepare_sel2
import torch.distributed as dist


class MoE(LoggingLayer, RegularizedLayer, OncePerIterLayer, torch.nn.Module):
    def __init__(self, dmodel: int, n_experts: int, expert_size: int, n_heads: int,
                 dropout: float = 0, weight_scale: float = 1.0,
                 selection_mode: str = "sigmoid", perplexity_reg: float = 0.0,
                 perplexity_reg_mode: str="step",
                 activation_after_topk: bool = False,
                 activation = lambda x: F.relu(x, inplace=True),
                 sel_bias: bool = False,
                 bias: bool = False,
                 v_dim: Optional[int] = None,
                 expert_dropout: float = 0.0,
                 sync_distributed: bool = False,
                 selection_dropout: float = 0.0,
                 log_interval: Optional[int] = 100,
                 ):

        super().__init__()
        self.k_dim = dmodel
        self.v_dim = v_dim if v_dim is not None else dmodel
        self.n_experts = n_experts
        self.expert_size = expert_size
        self.size = self.n_experts * self.expert_size
        self.dropout = dropout
        self.selection_mode = selection_mode
        self.perplexity_reg = perplexity_reg
        self.k_vec_dim = self.k_dim
        self.n_heads = n_heads
        self.perplexity_reg_mode = perplexity_reg_mode
        self.activation_after_topk = activation_after_topk
        self.activation = activation
        self.weight_scale = weight_scale
        self.layer = 0
        self.initalized = False
        self.was_training = True
        self.expert_dropout = expert_dropout
        self.reg_counts = 0
        self.sync_distributed = sync_distributed and torch.distributed.is_initialized()
        self.record_all_expert_sel_counts = False
        self.selection_dropout = selection_dropout
        self.log_interval = log_interval

        self.coocurence = None
        self.prev_sel_oh = None

        sel_weight_scale = weight_scale
        mid_layer_scale =  weight_scale


        assert self.selection_mode in {"sigmoid", "gate"}
        assert self.perplexity_reg_mode in {"global", "time", "step", "layers_time", "standard", "standard_with_zloss"}

        if self.perplexity_reg_mode in {"standard_with_zloss"}:
            self.z_loss_weight = 0.001

        self.new_counts_for_bias = None

        self.register_buffer("iter", torch.tensor(0, dtype=torch.int64), persistent=True)

        self.keys = torch.nn.Parameter(torch.empty(self.n_experts, self.k_vec_dim, self.expert_size))
        self.get_initializer()(self.keys, std=dmodel ** -0.5 * mid_layer_scale)

        if bias:
            self.bias = torch.nn.Parameter(torch.zeros(self.n_experts, self.expert_size))
            self.o_bias = torch.nn.Parameter(torch.zeros(self.v_dim))
        else:
            self.bias = None
            self.o_bias = None

        self.values = torch.nn.Parameter(torch.empty(self.n_experts, self.expert_size, self.v_dim))

        sel_count = self.n_experts

        self.expert_sel = torch.nn.Parameter(torch.empty(sel_count, self.k_vec_dim))
        self.sel_bias = torch.nn.Parameter(torch.zeros(sel_count)) if sel_bias else None

        self.sel = lambda x: F.linear(x, self.expert_sel, self.sel_bias)

        self.get_initializer()(self.expert_sel, std=self.k_vec_dim ** -0.5 * sel_weight_scale)

        real_size = self.size

        self.get_initializer()(self.values, std=real_size ** -0.5 * weight_scale)
        self.sel_hist = []
        self.index_sel_counts = 0
        self.index_sel_norm = 0

        self.index_sel_counts_100 = 0
        self.index_sel_norm_100 = 0
        self.index_sel_counts_per_layer = []
        self.index_sel_counts_per_layer_100 = 0

        self.sel_count_log = None

        self.all_expert_sel_counts = []
        self.all_expert_sel_soft = []

        self.register_buffer("kv_sel_counts", torch.zeros(self.n_experts, self.expert_size), persistent=False)
        self.register_buffer("kv_sel_counts_100", torch.zeros_like(self.kv_sel_counts))

        self.register_buffer("seq", torch.arange(max(self.n_heads, self.n_experts, self.k_dim, self.v_dim), dtype=torch.long), persistent=False)
        self.balance_loss_coef_comp = 0.01
        self.router_theta = 0.2
        self.router_loss_coef = 0.001
        self.warm_up = 0.05
        self.rate_flip = 0.05
        self.total_steps = None
        self.prob_flips_final = {}
        self.max_compete_in_iter = 5
        self.current_steps = 0
        self.register_buffer('prob_flips', torch.zeros(380000))

    def set_current_steps(self, step):
        self.current_steps = step
    def set_total_steps(self, id_layer=0):

        """
        Sets up the total steps for the layer and creates a balanced candidate tensor
        for the current layer. The candidate tensor is adjusted based on the cumulative
        frequency from previous layers to ensure that the threshold is not exceeded,
        and then broadcast across distributed processes.

        Args:
            id_layer (int): Identifier for the current layer.

        Returns:
            dict: Updated self.prob_flips_final containing candidate tensors for all layers.
        """
        # if self.training == False: return
        # Compute warm-up steps and determine the number of flip steps.
        self.step_warm = int(self.warm_up * self.total_steps)
        flip_steps = self.total_steps - self.step_warm
        self.flip_steps = flip_steps
        # return self.prob_flips_final
        if flip_steps <= 0:
            raise ValueError("self.total_steps - self.step_warm must be greater than 0.")

        # Determine distributed rank and world size.
        if dist.is_initialized():
            rank = dist.get_rank()
            world_size = dist.get_world_size()
        else:
            rank = 0
            world_size = 1

        # Set up the device.
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        def create_balanced_flip_current(cum_frequency):
            """
            Creates a boolean tensor for the current layer with shape [flip_steps].
            For each candidate position, if the random probability (based on self.rate_flip)
            is met but the cumulative frequency (from previous layers plus the current layer)
            would exceed self.max_compete_in_iter, the candidate is shifted left or right
            to find a valid position.

            Args:
                cum_frequency (Tensor): A tensor of shape [flip_steps] containing the cumulative
                                        count of True values from previous layers.

            Returns:
                Tensor: A boolean tensor indicating candidate flips for the current layer.
            """
            candidate_current = [False] * flip_steps  # Initialize candidates.
            candidate_origin = [False] * flip_steps
            freq_updated = cum_frequency.clone()        # Copy cumulative frequency for updates.

            for i in range(flip_steps):
                if torch.rand(1, device=device).item() < self.rate_flip:
                    candidate_origin[i] = True
                    if freq_updated[i] < self.max_compete_in_iter:

                        candidate_current[i] = True
                        freq_updated[i] += 1
                    else:
                        found = False
                        # Try shifting to the left.
                        for j in range(i - 1, -1, -1):
                            if (freq_updated[j] < self.max_compete_in_iter) and (not candidate_current[j]):
                                candidate_current[j] = True
                                freq_updated[j] += 1
                                found = True
                                break
                        # If left shift fails, try shifting to the right.
                        if not found:
                            for j in range(i + 1, flip_steps):
                                if (freq_updated[j] < self.max_compete_in_iter) and (not candidate_current[j]):
                                    candidate_current[j] = True
                                    freq_updated[j] += 1
                                    found = True
                                    break

            # print('+++++++++++++++++++++++++++++++++++++++++++++++++++')
            # print(f"Layer {id_layer}: {(torch.tensor(candidate_current, dtype=torch.bool, device=device) != torch.tensor(candidate_origin, dtype=torch.bool, device=device)).sum()}")
            with open("./file_path.txt", "a") as f:
                # Write the new log entry at the top
                f.write("+++++++++++++++++++++++++++++++++++++++++++++++++++\n")
                f.write(f"Layer {id_layer}: {(torch.tensor(candidate_current, dtype=torch.bool, device=device) != torch.tensor(candidate_origin, dtype=torch.bool, device=device)).sum()}\n")

            return torch.tensor(candidate_current, dtype=torch.bool, device=device)

        # Only rank 0 creates the candidate tensor.
        if rank == 0:
            from tqdm import tqdm  # Optional progress display.
            import os

            # Compute cumulative frequency from previous layers.
            if self.prob_flips_final:
                frequency_on_compete = torch.zeros(flip_steps, dtype=torch.int, device=device)
                for _, v in self.prob_flips_final.items():
                    frequency_on_compete += v.int()
            else:
                frequency_on_compete = torch.zeros(flip_steps, dtype=torch.int, device=device)
                os.environ["start_max"] = '1'

            probs_current = create_balanced_flip_current(frequency_on_compete)
        else:
            # Other ranks create an empty tensor to receive the broadcast.
            probs_current = torch.empty(flip_steps, dtype=torch.bool, device=device)

        # Broadcast the candidate tensor to all processes if in distributed mode.
        if world_size > 1:
            dist.broadcast(probs_current, src=0)

        # Validate the candidate flips.
        count_true = probs_current.sum().item()
        count_false = flip_steps - count_true
        ratio_true = count_true / flip_steps
        ratio_false = count_false / flip_steps

        # if ratio_true == 0.0 or ratio_false == 0.0:
        #     raise ValueError("Invalid ratio of True or False in candidate flips.")

        # Assign the final candidate tensor for the current layer only once.
        self.prob_flips_final[id_layer] = probs_current

        self.prob_flips =probs_current

        # save file
        import json
        save_weights = {}
        for layer in self.prob_flips_final.keys():
            save_weights[layer] = self.prob_flips_final[layer].tolist()
        # Save to a JSON file
        # Save to a JSON file
        with open("/cm/archive/thongdt4/moeut_training_code/save/slimpajama_competesmoe_no_attmoe_660M_standardlb/full_prob_competesmoe.json", "w") as json_file:
            json.dump(save_weights, json_file, indent=4)
        if rank == 0:
            print(f"Updated prob_flips_final keys: {list(self.prob_flips_final.keys())}")
            print(f"\nCompute Competition Rate (Layer {id_layer}): {ratio_true}")
            print(f"Compute Router Policy Rate: {ratio_false}")
            print(f"Warm-up Steps: {self.step_warm}\n")

        self.is_prob_flips = False
        return self.prob_flips_final

    def keys_to_logical_order(self, keys: torch.Tensor) -> torch.Tensor:
        k = keys.view(self.n_experts, self.k_vec_dim, self.expert_size)
        return k.permute(0, 2, 1).contiguous().view(-1, self.k_vec_dim)

    def keys_from_logical_order(self, keys: torch.Tensor) -> torch.Tensor:
        return keys.view(self.n_experts, self.expert_size, self.k_vec_dim).permute(0, 2, 1).contiguous().view(self.n_experts * self.k_vec_dim, self.expert_size)


    def renorm_keep_std(self, weight: torch.Tensor, dim: int = 0):
        with torch.no_grad():
            std = weight.std()
            weight.div_(weight.norm(dim=dim, keepdim=True))
            weight.mul_(std / weight.std())


    def fix_expert_sel_init(self):
        with torch.no_grad():
            self.renorm_keep_std(self.expert_sel, dim=1)

    def get_initializer(self):
        return torch.nn.init.normal_

    def sparse_matmul(self, indices: torch.Tensor, values: torch.Tensor, weight: torch.Tensor) -> torch.Tensor:
        return F.embedding_bag(indices, weight.type_as(values), per_sample_weights=values, mode="sum", sparse=False)

    def ani(self, x: torch.Tensor) -> torch.Tensor:
        assert x.ndim == 2
        chunk_size = 32

        xnorm = F.normalize(x, 2, dim=-1)

        accu = 0
        for i in range(0, x.shape[0], chunk_size):
            a = xnorm[i: i + chunk_size]
            sims = xnorm @ a.T
            sims[i : i + chunk_size].fill_diagonal_(0)
            accu += sims.sum()

        return accu / (x.shape[0] * (x.shape[0] - 1))

    def log_expert_sel_usage(self, prefix: str, channel_sel_counts: torch.Tensor):
        sel_nonzero = (channel_sel_counts != 0).type(torch.float).sum(axis=-1) / self.expert_size
        self.log(f"{prefix}/mean", sel_nonzero.mean())
        self.log(f"{prefix}/min", sel_nonzero.min())
        self.log(f"{prefix}/max", sel_nonzero.max())


    def pre_train_forward(self):
        self.prev_sel_oh = None

        if self.training and not self.was_training:
            sorted_counts = self.index_sel_counts.sort(descending=True).values
            self.log("test_exert_channel_usage", framework.visualize.plot.Barplot(sorted_counts, xlabel="expert", ylabel="usage count"), drop_old=True)

        self.layer = 0
        if self.sel_hist:
            self.sel_hist = []
        self.index_sel_counts = 0
        self.index_sel_norm = 0
        self.reg_counts = 0
        self.index_sel_counts_per_layer = []

    def before_loss(self):
        if self.sel_hist:
            # Concatenate against time dimension. Important for the within-batch regularization
            sel = torch.stack(self.sel_hist, 1)
            self.add_perplexity_reg(sel)

            self.sel_hist = []


        if self.training and len(self.index_sel_counts_per_layer) > 1:
            index_sel_counts_per_layer = torch.stack(self.index_sel_counts_per_layer, dim=0)
            self.index_sel_counts_per_layer = []

            if torch.is_tensor(self.index_sel_counts_per_layer_100) and self.index_sel_counts_per_layer_100.shape != index_sel_counts_per_layer.shape:
                # The number of layers changed
                if self.index_sel_counts_per_layer_100.shape[0] > index_sel_counts_per_layer.shape[0]:
                    # self.index_sel_counts_per_layer_100 = self.index_sel_counts_per_layer_100[:index_sel_counts_per_layer.shape[0]]
                    index_sel_counts_per_layer = F.pad(index_sel_counts_per_layer, [0, 0, 0, self.index_sel_counts_per_layer_100.shape[0] - index_sel_counts_per_layer.shape[0]])
                else:
                    self.index_sel_counts_per_layer_100 = F.pad(self.index_sel_counts_per_layer_100, [0, 0, 0, index_sel_counts_per_layer.shape[0] - self.index_sel_counts_per_layer_100.shape[0]])

            self.index_sel_counts_per_layer_100 += index_sel_counts_per_layer

            if self.iter % self.log_interval == 0:
                index_sel_counts_per_layer_100 = framework.utils.distributed_ops.reduce_any(self.index_sel_counts_per_layer_100)
                index_sel_counts_per_layer_100 = index_sel_counts_per_layer_100.float()
                index_sel_counts_per_layer_100 /= index_sel_counts_per_layer_100.sum(-1, keepdim=True)

                self.log("moe_per_layer_100", framework.visualize.plot.Heatmap(index_sel_counts_per_layer_100, xlabel="expert", ylabel="layer", textval=False), drop_old=True)

                total = index_sel_counts_per_layer_100.sum(-1, keepdim=True)
                pairwise_overlap = torch.min(index_sel_counts_per_layer_100.unsqueeze(0), index_sel_counts_per_layer_100.unsqueeze(1)).sum(-1)
                pairwise_overlap = pairwise_overlap / total

                self.log("layer_sel_similarity_100", framework.visualize.plot.Heatmap(pairwise_overlap, xlabel="layer", ylabel="layer", textval=False), drop_old=True)

                self.log("universal_score", pairwise_overlap.mean())
                self.log("universal_score_optimist", pairwise_overlap.max(-1).values.mean())


                self.index_sel_counts_per_layer_100 = 0

        if self.index_sel_norm > 0:
            if self.training and self.log_interval is not None:
                with torch.no_grad():
                    self.index_sel_counts_100 = self.index_sel_counts_100 + self.index_sel_counts
                    self.index_sel_norm_100 = self.index_sel_norm_100 + self.index_sel_norm

                    if self.iter % self.log_interval == 0:
                        self.log("usag_rel_perplexity_all_layers", utils.relative_perplexity(self.index_sel_counts / self.index_sel_norm))
                        self.log("dead_expert_proportion_all_layers", (self.index_sel_counts == 0).float().sum() / self.n_experts)

                        if self.sel_bias is not None:
                            self.log("sel_bias_min", self.sel_bias.detach().min())
                            self.log("sel_bias_max", self.sel_bias.detach().max())

                        index_sel_counts_100 = framework.utils.distributed_ops.reduce_any(self.index_sel_counts_100)
                        index_sel_norm_100 = framework.utils.distributed_ops.reduce_any(self.index_sel_norm_100)
                        norm_cnt = index_sel_counts_100 / index_sel_norm_100
                        self.log("usag_rel_perplexity_100", utils.relative_perplexity(norm_cnt))
                        self.log("dead_expert_proportion_100", (index_sel_counts_100 == 0).float().sum() / self.n_experts)

                        sorted_counts = index_sel_counts_100.sort(descending=True).values
                        self.log("usage_counts_100", framework.visualize.plot.Barplot(sorted_counts, xlabel="expert", ylabel="usage count"), drop_old=True)


                        self.index_sel_counts_100 = 0
                        self.index_sel_norm_100 = 0

                        self.log("ani/keys", self.ani(self.keys_to_logical_order(self.keys)))
                        self.log("ani/values", self.ani(self.values.flatten(0, -2)))
                        if self.expert_sel is not None:
                            self.log("ani/expert_sel", self.ani(self.expert_sel.T))

        if self.training:
            self.iter += 1

    def topk(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        return x.topk(k, dim=-1, sorted=True)

    def topk_except_top1(self, x: torch.Tensor, k: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        function to select the top-k but exclude the top-1.
        Instead of select top [1 -> k], we select top [2 -> (k+1)]
        """
        sel_val, sel_index =  x.topk(k + 1, dim=-1, sorted=True)
        return sel_val[:, :, 1:], sel_index[:, :, 1:]

    def logsoftmax_of_history(self, x: torch.Tensor) -> torch.Tensor:
        # Simulate calculating logsumexp over a bigger batch than the current one. Will have stale values, but that
        # should not matter much later in training.
        return F.log_softmax(x, dim=-1)

    def add_perplexity_reg(self, sel: torch.Tensor):
        sync_distributed = self.sync_distributed and (self.perplexity_reg_mode == "global")

        if self.perplexity_reg_mode in {"time", "layers_time"}:
            sel = sel.flatten(1, -2)
        elif self.perplexity_reg_mode == "global":
            sel = sel.flatten(0, -2)
        elif self.perplexity_reg_mode == "step":
            sel = sel.flatten(0, -2).unsqueeze(-2)
        else:
            raise ValueError(f"Unknown perplexity_reg_mode: {self.perplexity_reg_mode}")

        sel = sel.float()

        # Note: sel are raw logits, no matter what activation is used
        if self.perplexity_reg > 0:
            sel_d = self.logsoftmax_of_history(sel)
            sel_d = framework.utils.distributed_ops.log_mean(sel_d, -2, sync_distributed)
            loss = lambda: self.perplexity_reg * ( - utils.entropy_l(sel_d).mean())

            self.add_reg(loss, "moe")

    def add_perplexity_reg_standard(self, sel_aux: torch.Tensor, sel_index: torch.Tensor, bsz: int, seq_len: int):
        sel_aux_scaled = sel_aux / sel_aux.sum(-1).unsqueeze(-1)
        aux_loss = torch.zeros(bsz, self.n_experts, device=sel_aux.device)
        aux_loss.scatter_add_(1, sel_index.view(bsz, seq_len * self.n_heads), torch.ones(bsz, seq_len * self.n_heads, device=sel_aux.device)).div_(seq_len * self.n_heads / self.n_experts)

        if self.perplexity_reg > 0:
            loss = lambda: self.perplexity_reg * (aux_loss * sel_aux_scaled.mean(dim = 1)).sum(dim = 1).mean()

            self.add_reg(loss, "moe")

    def add_perplexity_reg_standard_comp(self, sel_aux: torch.Tensor, sel_index: torch.Tensor, bsz: int, seq_len: int, bal_coef: int):
        sel_aux_scaled = sel_aux / sel_aux.sum(-1).unsqueeze(-1)
        aux_loss = torch.zeros(bsz, self.n_experts, device=sel_aux.device)
        aux_loss.scatter_add_(1, sel_index.view(bsz, seq_len * self.n_heads), torch.ones(bsz, seq_len * self.n_heads, device=sel_aux.device)).div_(seq_len * self.n_heads / self.n_experts)

        if bal_coef > 0:
            loss = lambda: bal_coef * (aux_loss * sel_aux_scaled.mean(dim = 1)).sum(dim = 1).mean()

            self.add_reg(loss, "moe")

    def add_perplexity_reg_standard_with_zloss(self, sel_aux: torch.Tensor, sel_index: torch.Tensor, bsz: int, seq_len: int):
        sel_aux_scaled = sel_aux / sel_aux.sum(-1).unsqueeze(-1)
        aux_loss = torch.zeros(bsz, self.n_experts, device=sel_aux.device)
        aux_loss.scatter_add_(1, sel_index.view(bsz, seq_len * self.n_heads), torch.ones(bsz, seq_len * self.n_heads, device=sel_aux.device)).div_(seq_len * self.n_heads / self.n_experts)

        if self.perplexity_reg > 0:
            balancing_loss = self.perplexity_reg * (aux_loss * sel_aux_scaled.mean(dim = 1)).sum(dim = 1).mean()
        else:
            balancing_loss = 0

        # compute z loss
        if self.z_loss_weight > 0:
            router_z_loss = torch.logsumexp(sel_aux, dim=-1)
            router_z_loss = torch.square(router_z_loss)
            router_z_loss = router_z_loss.mean()

            z_loss = self.z_loss_weight * router_z_loss
        else:
            z_loss = 0

        loss = lambda: balancing_loss + z_loss
        self.add_reg(loss, "moe")


    def compute_scores(self, input: torch.Tensor, index: CVMMSel) -> Tuple[torch.Tensor, torch.Tensor]:
        if self.keys is not None:
            scores = cvmm(input, index, self.keys)

        if self.bias is not None:
            scores = scores + self.bias[index.raw_sel]


        scores = self.activation(scores)


        plot_training = self.train and self.log_interval is not None and self.iter % self.log_interval == 0
        if plot_training:
            with torch.no_grad():
                gt0 = (scores > 0).float()
                gt0_s = gt0.sum()

                if plot_training:
                    self.log("relu_pass_rate", gt0_s / scores.numel())

        return scores

    def sel_activation(self, sel: torch.Tensor, seq_len: int) -> Tuple[torch.Tensor, torch.Tensor]:
        reg_sel = sel
        if self.selection_mode in {"sigmoid"}:
            temperature = 1.0
            sel = torch.sigmoid(sel / temperature)
        elif self.selection_mode in {"gate"}:
            temperature = 1.0
            sel = F.softmax(sel / temperature, dim=-1)
            with torch.no_grad():
                self.log("expert_rel_perplexity_per_selection", utils.relative_perplexity(sel).mean())
        else:
            assert False

        return sel, reg_sel
    def router_loss(self, gate_softmax, affinity_softmax):

        """
        Computes the router loss, which encourages the gate's softmax probabilities to match the affinity scores.

        Args:
            gate_softmax (tensor): Softmax probabilities from the gate logits of shape (B, N, num_of_experts).
            affinity_softmax (tensor): Softmax probabilities of the affinity scores of shape (B, N, num_of_experts).

        Returns:
            loss (tensor): Scalar tensor representing the mean squared error (MSE) between the gate and affinity softmax probabilities.
        """

        loss = F.mse_loss(gate_softmax, affinity_softmax)
        return loss

    def competition_policy_mlp_faster(self, x):
        """
        Implements the competition policy for expert selection.

        Args:
            x (tensor): Input tensor of shape (B, N, D), where:
                - B: Batch size
                - N: Sequence length
                - D: Input feature dimension

        Returns:
            weights (tensor): Tensor of shape (B, N, num_selected) representing the normalized weights for the selected experts.
            selected_experts (tensor): Tensor of shape (B, N, num_selected) containing the indices of the selected experts.
            affinity_softmax (tensor): Softmax probabilities of the affinity scores, with shape (B, N, num_of_experts).
        """
        B, N, D = x.shape
        expert_outputs = []
        affinity_scores = torch.zeros(B, N, self.n_experts, device=x.device, dtype=x.dtype)
        expert_outputs = torch.matmul(x.view(-1, x.size(-1)), self.keys)
        expert_outputs = self.activation(expert_outputs)
        expert_outputs = torch.matmul(expert_outputs, self.values)
        expert_outputs = expert_outputs.transpose(1, 0) # (B*N, E, D)
        affinity_scores = torch.mean(F.softplus(expert_outputs), dim = -1)

        affinity_scores = affinity_scores.view(x.shape[0], x.shape[1], affinity_scores.shape[-1]) # (B, N, E)
        affinity_softmax = F.softmax(affinity_scores, dim=-1)
        weights, selected_experts = torch.topk(affinity_scores, self.n_heads)
        # weights = weights / torch.sum(weights, dim=-1, keepdim=True).to(x.dtype)
        denominator = weights.sum(dim=-1, keepdim=True) + 1e-20
        weights = weights / denominator
        # compute input for diversity loss
        idx_expanded = selected_experts.unsqueeze(-1).expand(B, N, self.n_heads, expert_outputs.size(-1))
        expert_outputs = expert_outputs.view(*x.shape[:2], *expert_outputs.shape[1:])
        topk_expert_outputs = torch.gather(expert_outputs, dim=2, index=idx_expanded)

        return weights, selected_experts, affinity_softmax, affinity_scores, topk_expert_outputs
    def experts_diversity_loss(self, expert_outputs):
        """
        expert_outputs: Tensor shape [B, N, K, D]
            - B: batch size
            - N: sequence length
            - K: number of selected experts
            - D: dimension of each expert output

        Mục tiêu: phạt khi các expert outputs 'quá giống nhau'.
        Ta sẽ tính độ tương đồng cos trung bình giữa mọi cặp (i, j) trong K experts, rồi lấy mean.
        """
        expert_outputs = expert_outputs.to(torch.float32)
        if len(expert_outputs.shape) == 5:
            expert_outputs = expert_outputs.view(expert_outputs.shape[0], expert_outputs.shape[1] * expert_outputs.shape[2], *expert_outputs.shape[3:])
        B, N, K, D = expert_outputs.shape

        # Bước 1: Chuẩn hoá (L2-normalize) theo chiều D để tính Cosine Similarity
        # Shape sau chuẩn hoá vẫn là [B, N, K, D]
        normalized = F.normalize(expert_outputs, p=2, dim=-1)

        # Bước 2: Đưa (B, N) về 1 batch lớn để dễ tính bmm
        # Ta reshape thành [B*N, K, D]
        normalized_reshape = normalized.view(B*N, K, D)  # => [B*N, K, D]

        # Bước 3: Tính ma trận similarity bằng bmm:
        # [B*N, K, D] x [B*N, D, K] -> [B*N, K, K]
        similarity_matrix = torch.bmm(
            normalized_reshape,
            normalized_reshape.transpose(1, 2)
        )  # => [B*N, K, K]

        # Bước 4: Loại bỏ độ tương đồng với chính nó (đường chéo)
        # identity = [K, K], shape broadcast được cho [B*N, K, K]
        mask = 1 - torch.eye(K, device=expert_outputs.device)
        similarity_matrix = similarity_matrix * mask
        # nb_diver = (similarity_matrix != 0).sum()
        # Bước 5: Tính trung bình trên tất cả các batch, token, và cặp expert
        # similarity_matrix có shape [B*N, K, K]. Số phần tử hợp lệ = B*N * K * (K-1)
        loss = similarity_matrix.mean()
        return loss
    def compute_moe_main(self, x, selected_experts, weights):

        # weights = weights / torch.sum(weights, dim=-1, keepdim=True).to(x.dtype)
        sel_indices = cvmm_prepare_sel2(selected_experts.int())
        scores = self.compute_scores(x, sel_indices)

        sel_indices = sel_indices.clone()
        sel_indices.reduction_weight = weights
        sel_indices.sel_index = sel_indices.out_index
        sel_indices.out_index = None

        out = cvmm(scores, sel_indices, self.values)
        return out
    def forward(self, input: torch.Tensor,  *args, **kwargs) -> torch.Tensor:
        id_layer = kwargs['id_layer']
        assert id_layer is not None, "Layer Id must to not None"
        is_comp = input.requires_grad and self.current_steps >= self.step_warm and self.prob_flips[self.current_steps - self.step_warm].item() == 1

        bsz, seq_len, h = input.shape
        in1 = in2 = in3_comp = input

        sel_input = in1
        if self.selection_dropout > 0 and self.training:
            sel_input = F.dropout(sel_input, self.selection_dropout)

        sel = self.sel(sel_input)
        sel_raw = reg_sel = sel

        inv_val = float("-inf")

        if not self.activation_after_topk:
            sel, reg_sel = self.sel_activation(sel, input.shape[-2])
        sel_aux = sel

        if self.training and self.expert_dropout > 0:
            mask = torch.rand_like(sel) < self.expert_dropout
            sel2 = sel.masked_fill(mask, inv_val)
        else:
            sel2 = sel
        sel_val, sel_index = self.topk(sel2, self.n_heads)
        if is_comp:
            affinity_weights, affinity_selected_experts, affinity_softmax, affinity_logits, expert_outputs = self.competition_policy_mlp_faster(in3_comp)

            out = self.compute_moe_main(
                selected_experts=affinity_selected_experts,
                weights=affinity_weights,
                x=in3_comp,
            )

            # diversity loss
            comp_diver_loss = self.experts_diversity_loss(expert_outputs)
            self.add_reg(lambda: comp_diver_loss * self.balance_loss_coef_comp, "_comp_diver_loss")

            # router losss
            gate_softmax_topk = torch.gather(sel, dim=-1, index=affinity_selected_experts)
            affinity_softmax_topk = torch.gather(affinity_softmax, dim=-1, index=affinity_selected_experts)

            router_loss = self.router_loss(
                affinity_softmax=affinity_softmax.detach(),
                gate_softmax=sel

            )  + self.router_loss(
                affinity_softmax=affinity_softmax_topk.detach(),
                gate_softmax=gate_softmax_topk

            ) * self.router_theta
            self.add_reg(lambda: router_loss * self.router_loss_coef,  "_router_loss")

            self.add_perplexity_reg_standard_comp(affinity_softmax, affinity_selected_experts, bsz, seq_len, bal_coef=self.balance_loss_coef_comp / 2)


        else:

            ### norm gate to sum 1
            denominator = sel_val.sum(dim=-1, keepdim=True) + 1e-20
            sel_val = sel_val / denominator


            if self.activation_after_topk:
                sel_val = torch.gather(sel_raw, -1, sel_index)
                sel_val, reg_sel = self.sel_activation(sel_val, input.shape[-2])


            if reg_sel is not None:
                if self.perplexity_reg_mode in {"step", "time"}:
                    self.add_perplexity_reg(reg_sel)
                elif self.perplexity_reg_mode in {"standard"}:
                    self.add_perplexity_reg_standard(sel_aux, sel_index, bsz, seq_len)
                elif self.perplexity_reg_mode in {"standard_with_zloss"}:
                    self.add_perplexity_reg_standard_with_zloss(sel_aux, sel_index, bsz, seq_len)
                elif self.perplexity_reg > 0 and self.training:
                    self.sel_hist.append(reg_sel)

            # # sel_indices = [cvmm_prepare_sel(sel_index[..., h].int(), self.n_experts) for h in range(sel_index.shape[-1])]
            sel_indices = cvmm_prepare_sel2(sel_index.int())

            scores = self.compute_scores(in2, sel_indices)

            sel_indices = sel_indices.clone()
            sel_indices.reduction_weight = sel_val
            sel_indices.sel_index = sel_indices.out_index
            sel_indices.out_index = None

            # breakpoint()
            out = cvmm(scores, sel_indices, self.values)

        record_counts_now = (self.training and self.iter % 10 == 0) or (not self.training) or (self.record_all_expert_sel_counts)

        if not self.training:
            sel_index_flat = sel_index.flatten(end_dim=-2)
            if self.coocurence is None:
                self.coocurence = torch.zeros([self.n_experts, self.n_experts], device=sel_index_flat.device, dtype=torch.long)

            for h1 in range(self.n_heads):
                for h2 in range(self.n_heads):
                    ind_flat = sel_index_flat[..., h1] * self.n_experts + sel_index_flat[..., h2]
                    values = torch.tensor([1], device=self.coocurence.device, dtype=self.coocurence.dtype).expand_as(ind_flat)
                    # values = sel_val[..., h2].flatten()
                    self.coocurence.flatten().put_(ind_flat, values, accumulate=True)
                    # self.coocurence[sel_index_flat[..., h1], sel_index_flat[..., h2]] += 1
        if record_counts_now:
            reg_counts = F.one_hot(sel_index, self.n_experts).type_as(input)

            with torch.no_grad():
                sel_counts = reg_counts.flatten(end_dim=-2).sum(0)
                cnt = sel_index.nelement()

                # p_expert_sel = sel_counts / cnt

                self.index_sel_counts = self.index_sel_counts + sel_counts
                self.index_sel_norm = self.index_sel_norm + cnt

                if self.record_all_expert_sel_counts:

                    softcnt = torch.zeros_like(sel_counts, dtype=sel_val.dtype)
                    softcnt.index_add_(0, sel_index.flatten(), sel_val.flatten())

                    self.all_expert_sel_soft.append(softcnt)
                    self.all_expert_sel_counts.append(sel_counts)

                if self.training and self.log_interval is not None:
                    self.index_sel_counts_per_layer.append(sel_counts)

                    if self.iter % self.log_interval == 0:
                        self.log("min_sel_score", sel_val.min(dim=-1).values.mean())
                        self.log("max_sel_score", sel_val.max(dim=-1).values.mean())

                        sel_oh = F.one_hot(sel_index, self.n_experts).sum(-2).bool()
                        if self.prev_sel_oh is not None and self.training:
                            self.log(f"layer_sel_overlap_{self.layer}", ((self.prev_sel_oh & sel_oh).sum(-1).float() / self.n_heads).mean())

                        self.prev_sel_oh = sel_oh

        self.layer += 1

        self.was_training = self.training
        res = out.view(*input.shape[:-1], self.v_dim)
        if self.o_bias is not None:
            res = res + self.o_bias
        return res

    def get_logs(self) -> Dict[str, Any]:
        res = super().get_logs()

        if self.coocurence is not None:
            coo = self.coocurence / self.coocurence.diagonal().clamp(min=1)[:, None]
            res["expert_coocurence"] = framework.visualize.plot.Heatmap(coo, xlabel="expert", ylabel="expert", textval=False)
            self.coocurence = None
        return res

